{"$schema": "https://schema.tauri.app/config/2.5.1", "productName": "Ave App Store", "version": "0.1.0", "identifier": "com.avehub.appstore", "build": {"beforeBuildCommand": "npm run build", "devUrl": "https://avehubs.com/windows/apps"}, "app": {"windows": [{"title": "Ave App Store", "width": 800, "height": 600, "resizable": true, "fullscreen": false, "url": "https://avehubs.com/windows/apps"}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}