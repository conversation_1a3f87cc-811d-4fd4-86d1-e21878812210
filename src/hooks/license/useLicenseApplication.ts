'use client';
import { useState } from 'react';

interface LicenseData {
  spigotUsername: string;
  screenshot: File;
}

export function useLicenseApplication() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cooldownError, setCooldownError] = useState<{
    message: string;
    remainingTime: number;
    nextAllowedTime: string;
  } | null>(null);

  const apply = async (data: LicenseData) => {
    setLoading(true);
    setError(null);
    setCooldownError(null);

    try {
      const form = new FormData();
      form.append('spigotUsername', data.spigotUsername);
      form.append('screenshot', data.screenshot);
      const res = await fetch('/api/license/apply', { method: 'POST', body: form });
      const result = await res.json();

      if (!res.ok) {
        if (res.status === 429 && result.error === 'Cooldown active') {
          setCooldownError({
            message: result.message,
            remainingTime: result.remainingTime,
            nextAllowedTime: result.nextAllowedTime
          });
        } else {
          setError(result.error || 'Failed to submit application');
        }
        return null;
      }

      return result.request;
    } catch (e: any) {
      setError(e.message || 'Network error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { apply, loading, error, cooldownError };
}
