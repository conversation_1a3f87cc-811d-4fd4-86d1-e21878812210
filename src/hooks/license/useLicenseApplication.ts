'use client';
import { useState } from 'react';

interface LicenseData {
  spigotUsername: string;
  screenshot: File;
}

export function useLicenseApplication() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const apply = async (data: LicenseData) => {
    setLoading(true);
    setError(null);
    try {
      const form = new FormData();
      form.append('spigotUsername', data.spigotUsername);
      form.append('screenshot', data.screenshot);
      const res = await fetch('/api/license/apply', { method: 'POST', body: form });
      const result = await res.json();
      if (!res.ok) throw new Error(result.error || 'Failed');
      return result.request;
    } catch (e: any) {
      setError(e.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { apply, loading, error };
}
