'use client';
import { useState } from 'react';
import { toast } from 'sonner';
import useAdmin from '@/hooks/admin/useAdmin';

export function useUpdateLicenseStatus() {
  const [processingId, setProcessingId] = useState<string | null>(null);
  const { admin } = useAdmin();

  const updateStatus = async (id: string, status: 'approved' | 'rejected') => {
    if (!admin) {
      toast.error("No permission");
      return null;
    }
    setProcessingId(id);
    try {
      const res = await fetch(`/api/admin/license/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || 'Failed');
      toast.success('Updated');
      return data;
    } catch (e: any) {
      toast.error(e.message);
      return null;
    } finally {
      setProcessingId(null);
    }
  };

  return { updateStatus, processingId };
}
