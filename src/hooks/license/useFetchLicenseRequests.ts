'use client';
import { useState, useEffect } from 'react';
import useAdmin from '@/hooks/admin/useAdmin';

export interface LicenseRequestAdmin {
  id: string;
  userId: string;
  spigotUsername: string;
  screenshotUrl: string;
  status: string;
  requestedAt: string;
}

export function useFetchLicenseRequests() {
  const [requests, setRequests] = useState<LicenseRequestAdmin[]>([]);
  const [loading, setLoading] = useState(true);
  const { admin } = useAdmin();

  useEffect(() => {
    if (!admin) return;
    async function fetchAll() {
      setLoading(true);
      const res = await fetch('/api/admin/license');
      const data = await res.json();
      if (res.ok) setRequests(data); else console.error(data.error);
      setLoading(false);
    }
    fetchAll();
  }, [admin]);

  return { requests, loading };
}
