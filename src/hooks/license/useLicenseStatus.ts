'use client';
import { useState, useEffect } from 'react';

export interface LicenseRequest {
  id: string;
  userId: string;
  spigotUsername: string;
  screenshotUrl: string;
  status: string;
  requestedAt: string;
  updatedAt: string;
}

export interface CooldownInfo {
  active: boolean;
  remainingTime?: number;
  nextAllowedTime?: string;
}

export function useLicenseStatus() {
  const [requests, setRequests] = useState<LicenseRequest[]>([]);
  const [cooldown, setCooldown] = useState<CooldownInfo>({ active: false });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/license/status');
      const data = await res.json();
      if (res.ok) {
        setRequests(data.requests || []);
        setCooldown(data.cooldown || { active: false });
      } else {
        setError(data.error || 'Failed to fetch license status');
      }
    } catch (err) {
      setError('Network error');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return {
    requests,
    cooldown,
    loading,
    error,
    refetch: fetchStatus,
    // Legacy compatibility
    request: requests[0] || null
  };
}
