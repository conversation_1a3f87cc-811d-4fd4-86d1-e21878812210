'use client';
import { useState, useEffect } from 'react';

export interface LicenseRequest {
  id: string;
  userId: string;
  spigotUsername: string;
  screenshotUrl: string;
  status: string;
  requestedAt: string;
}

export function useLicenseStatus() {
  const [request, setRequest] = useState<LicenseRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStatus() {
      setLoading(true);
      setError(null);
      const res = await fetch('/api/license/status');
      const data = await res.json();
      if (res.ok) {
        setRequest(data.request);
      } else {
        setError(data.error || 'Failed');
      }
      setLoading(false);
    }
    fetchStatus();
  }, []);

  return { request, loading, error };
}
