"use client";

import { useState } from "react";
import { toast } from "sonner";
import useAdmin from "@/hooks/admin/useAdmin";

export function useDeleteStaffApplication() {
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { admin } = useAdmin();

  const sendDeletionEmail = async (email: string, fullName: string) => {
    if (!email) return;
    
    try {
      const response = await fetch("/api/mail/sender", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        body: JSON.stringify({
          email,
          title: "AveHub Staff Application Removed",
          description: `
            <p>Dear ${fullName},</p>
            <p>This is to inform you that your application to join the staff team at AveHub has been removed from our system.</p>
            <p>This could be due to one of the following reasons:</p>
            <ul>
              <li>You requested the removal of your application</li>
              <li>The position is no longer available</li>
              <li>Administrative review resulted in removal</li>
            </ul>
            <p>If you did not request this action and have questions, please feel free to contact our HR team.</p>
          `,
          footer: "If you have any questions, please contact our HR <NAME_EMAIL>."
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to send deletion notification: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      console.error("Error sending deletion notification:", error);
      return false;
    }
  };

  const deleteApplication = async (id: string) => {
    if (!admin) {
      toast.error("You don't have permission to perform this action");
      return false;
    }

    setDeletingId(id);
    
    try {
      const applicationResponse = await fetch(`/api/staffapplications/${id}`, {
        method: "GET",
        headers: {
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
      });
      
      if (!applicationResponse.ok) {
        throw new Error(`Failed to get application details: ${applicationResponse.status}`);
      }
      
      const applicationData = await applicationResponse.json();
      
      const response = await fetch(`/api/staffapplications/${id}`, {
        method: "DELETE",
        headers: {
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error: ${response.status}` }));
        throw new Error(errorData.error || `Failed to delete application with status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success("Staff application deleted successfully");
        
        if (applicationData && applicationData.email) {
          await sendDeletionEmail(applicationData.email, applicationData.fullName);
        }
        
        return true;
      } else {
        throw new Error(result.message || "Failed to delete application");
      }
    } catch (error) {
      console.error("Error deleting staff application:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete application");
      return false;
    } finally {
      setDeletingId(null);
    }
  };

  return { deleteApplication, deletingId };
} 