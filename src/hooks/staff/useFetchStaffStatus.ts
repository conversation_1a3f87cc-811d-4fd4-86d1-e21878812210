"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

interface StaffApplication {
  id: string;
  userId: string;
  fullName: string;
  age: number;
  location: string;
  availability: string;
  position: string;
  experience: string;
  skills: string;
  motivation: string;
  portfolioUrl?: string | null;
  additionalInfo?: string | null;
  status: string;
  appliedAt: Date;
  statusUpdatedAt?: Date | null;
}

interface FetchResponse {
  application: StaffApplication | null;
  isLoading: boolean;
  error: string | null;
  mutate: () => void;
}

export function useStaffStatus(userId: string | undefined): FetchResponse {
  const [application, setApplication] = useState<StaffApplication | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!userId) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/staffapplication/status?userId=${userId}`);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error: ${response.status}` }));
        throw new Error(errorData.error || `Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      }
      
      setApplication(data.application);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error("Failed to fetch staff application status:", err);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Initial fetch
  useEffect(() => {
    if (userId) {
      fetchData();
    }
  }, [userId, fetchData]);

  // Manual refetch function
  const mutate = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    application,
    isLoading,
    error,
    mutate,
  };
} 