"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";

export interface StaffApplication {
  id: string;
  userId: string;
  fullName: string;
  age: number;
  email: string;
  location: string;
  position: string;
  experience: string;
  skills: string;
  availability: string;
  motivation: string;
  portfolioUrl?: string;
  additionalInfo?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export function useFetchStaffApplications() {
  const [applications, setApplications] = useState<StaffApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { status } = useSession();
  const hasInitiallyFetched = useRef(false);

  const fetchApplications = useCallback(async (forceFetch = false) => {
    // Only fetch if user is authenticated
    if (status !== "authenticated") {
      return;
    }
    
    // Skip if we've already fetched and this isn't a force refresh
    if (hasInitiallyFetched.current && !forceFetch) {
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const res = await fetch("/api/staffapplications", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        // Add cache control to prevent refetching
        cache: 'no-store'
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || errorData.message || `HTTP error: ${res.status}`);
      }

      const data = await res.json();
      setApplications(data);
      hasInitiallyFetched.current = true;
    } catch (err) {
      console.error("Error fetching staff applications:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setLoading(false);
    }
  }, [status]); // Only depend on auth status

  // Effect to run ONCE after auth is determined
  useEffect(() => {
    if (status === "authenticated" && !hasInitiallyFetched.current) {
      fetchApplications();
    }
  }, [status, fetchApplications]);

  // Make sure refreshApplications is memoized and won't change on rerenders
  const refreshApplications = useCallback(() => {
    fetchApplications(true);
  }, [fetchApplications]);

  return { 
    applications, 
    loading, 
    error, 
    refreshApplications 
  };
} 