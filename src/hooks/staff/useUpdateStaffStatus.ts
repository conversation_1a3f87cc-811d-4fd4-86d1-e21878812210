"use client";

import { useState } from "react";
import { toast } from "sonner";
import { StaffApplication } from "./useFetchStaffApplications";
import useAdmin from "@/hooks/admin/useAdmin";

export function useUpdateStaffStatus() {
  const [processingId, setProcessingId] = useState<string | null>(null);
  const { admin } = useAdmin();

  const updateStatus = async (
    id: string, 
    status: "approved" | "rejected" | "accepted",
    onSuccess?: (application: StaffApplication) => void
  ) => {
    if (!admin) {
      toast.error("You don't have permission to perform this action");
      return null;
    }

    setProcessingId(id);
    try {
      const response = await fetch(`/api/staffapplications/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update staff application status");
      }

      const updatedApplication = await response.json();
      
      toast.success(`Staff application ${status === "approved" || status === "accepted" ? "approved" : "rejected"} successfully`);
      
      if (onSuccess) {
        onSuccess(updatedApplication);
      }
      
      return updatedApplication;
    } catch (error) {
      console.error("Error updating staff application status:", error);
      toast.error("Failed to update staff application status");
      return null;
    } finally {
      setProcessingId(null);
    }
  };

  const sendStatusEmail = async (application: StaffApplication, status: "approved" | "rejected" | "accepted") => {
    try {
      if (!application.email) {
        console.warn("Cannot send email: missing email address");
        return false;
      }

      const response = await fetch("/api/mail/sender", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        body: JSON.stringify({
          email: application.email,
          title: `Staff Application ${status === "approved" || status === "accepted" ? "Approved" : "Update"}`,
          description: status === "approved" || status === "accepted"
            ? `<p>Dear ${application.fullName},</p>
               <p>We are pleased to inform you that your staff application with AveHub has been approved. Welcome to the team!</p>
               <p>Our HR team will contact you shortly with next steps, including onboarding information and details about your role as ${application.position}.</p>
               <p>Please make sure to check your email regularly for further communications.</p>`
            : `<p>Dear ${application.fullName},</p>
               <p>Thank you for your interest in joining the AveHub team. After careful consideration of your application for the ${application.position} position, we regret to inform you that we will not be moving forward with your candidacy at this time.</p>
               <p>While we were impressed with your skills and experience, we have decided to pursue other candidates whose qualifications more closely align with our current needs.</p>
               <p>We appreciate your interest in AveHub and wish you success in your future endeavors.</p>`,
          footer: "If you have any questions, please contact our HR <NAME_EMAIL>."
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error: ${response.status}` }));
        console.error("Email API error:", errorData);
        throw new Error(errorData.error || `Email notification failed with status: ${response.status}`);
      }
      
      toast.success("Email notification sent to applicant");
      return true;
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error(`Failed to send email notification: ${error instanceof Error ? error.message : "Unknown error"}`);
      return false;
    }
  };

  return { 
    updateStatus, 
    sendStatusEmail, 
    processingId 
  };
} 