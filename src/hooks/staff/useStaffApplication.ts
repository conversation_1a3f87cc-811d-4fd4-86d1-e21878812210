"use client";

import { useState } from "react";

interface ApplicationData {
  userId: string;
  fullName: string;
  age: number;
  location: string;
  availability: string;
  position: string;
  experience: string;
  skills: string;
  motivation: string;
  portfolioUrl?: string;
  additionalInfo?: string;
}

interface ApplicationResponse {
  message?: string;
  error?: string;
  application?: any;
}

export function useStaffApplication() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const applyForStaffPosition = async (data: ApplicationData) => {
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch("/api/staffapplication/post", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        body: JSON.stringify(data),
      });

      const result: ApplicationResponse = await response.json();

      if (!response.ok) throw new Error(result.error || "Something went wrong");

      setSuccessMessage(result.message || "Application submitted successfully!");
      return result.application;
    } catch (err: any) {
      setError(err.message || "Failed to submit application.");
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { applyForStaffPosition, loading, error, successMessage };
} 