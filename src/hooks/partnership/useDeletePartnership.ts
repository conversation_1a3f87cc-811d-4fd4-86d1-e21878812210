import { useState } from 'react';
import { toast } from 'sonner';
import useAdmin from '@/hooks/admin/useAdmin';

export function useDeletePartnership() {
  const [deleting, setDeleting] = useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { admin } = useAdmin();

  const deletePartnership = async (id: string) => {
    if (!admin) {
      toast.error("You don't have permission to delete partnerships");
      return false;
    }

    setDeleting(true);
    setDeletingId(id);

    try {
      const response = await fetch(`/api/partnerships/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete partnership');
      }

      toast.success('Partnership application deleted successfully');
      return true;
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Failed to delete partnership');
      }
      return false;
    } finally {
      setDeleting(false);
      setDeletingId(null);
    }
  };

  return {
    deletePartnership,
    deleting,
    deletingId
  };
} 