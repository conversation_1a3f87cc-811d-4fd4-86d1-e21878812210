import { useState } from "react";
import { toast } from "sonner";
import { Partnership } from "./useFetchPartnerships";
import useAdmin from "@/hooks/admin/useAdmin";

export function useUpdatePartnershipStatus() {
  const [processingId, setProcessingId] = useState<string | null>(null);
  const { admin } = useAdmin();

  const updateStatus = async (
    id: string, 
    status: "approved" | "rejected" | "accepted",
    onSuccess?: (partnership: Partnership) => void
  ) => {
    if (!admin) {
      toast.error("You don't have permission to perform this action");
      return null;
    }

    setProcessingId(id);
    try {
      const response = await fetch(`/api/partnerships/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update partnership status");
      }

      const updatedPartnership = await response.json();
      
      toast.success(`Partnership ${status === "approved" || status === "accepted" ? "approved" : "rejected"} successfully`);
      
      if (onSuccess) {
        onSuccess(updatedPartnership);
      }
      
      return updatedPartnership;
    } catch (error) {
      console.error("Error updating partnership status:", error);
      toast.error("Failed to update partnership status");
      return null;
    } finally {
      setProcessingId(null);
    }
  };

  const sendStatusEmail = async (partnership: Partnership, status: "approved" | "rejected" | "accepted") => {
    try {
      const response = await fetch("/api/mail/sender", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        body: JSON.stringify({
          email: partnership.email,
          title: `Partnership Application ${status === "approved" || status === "accepted" ? "Approved" : "Update"}`,
          description: status === "approved" || status === "accepted"
            ? `<p>Dear ${partnership.name},</p>
               <p>We are pleased to inform you that your partnership application with AveHub has been approved. We look forward to a successful collaboration.</p>
               <p>Our team will contact you shortly with next steps.</p>`
            : `<p>Dear ${partnership.name},</p>
               <p>Thank you for your interest in partnering with AveHub. After careful consideration, we regret to inform you that we are unable to proceed with your partnership application at this time.</p>
               <p>We appreciate your understanding and wish you success in your future endeavors.</p>`,
          footer: "If you have any questions, please contact our support team."
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error: ${response.status}` }));
        console.error("Email API error:", errorData);
        throw new Error(errorData.error || `Email notification failed with status: ${response.status}`);
      }
      
      toast.success("Email notification sent successfully");
      return true;
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error(`Failed to send email notification: ${error instanceof Error ? error.message : "Unknown error"}`);
      return false;
    }
  };

  return { 
    updateStatus, 
    sendStatusEmail, 
    processingId 
  };
} 