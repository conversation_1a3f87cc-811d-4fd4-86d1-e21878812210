"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import useAdmin from "@/hooks/admin/useAdmin";

export interface Partnership {
  id: string;
  name: string;
  email: string;
  companyName: string;
  websiteUrl: string;
  reason: string;
  experience: string;
  additional?: string | null;
  status: string;
  createdAt: string | Date;
}

export function useFetchPartnerships() {
  const [partnerships, setPartnerships] = useState<Partnership[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { data: session, status } = useSession();
  const { admin, loading: adminLoading } = useAdmin();
  const fetchedRef = useRef(false);
  const [retryCount, setRetryCount] = useState(0);

  const fetchPartnerships = useCallback(async () => {
    // Clear any previous errors
    setError(null);
    
    // Don't fetch if not logged in or not an admin
    if (status !== "authenticated") {
      setLoading(false);
      return;
    }

    // Wait for admin status to be determined
    if (adminLoading) {
      return;
    }

    // If not admin, don't attempt to fetch
    if (!admin) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      console.log("Fetching partnerships from API");
      const response = await fetch("/api/partnerships", {
        headers: {
          "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
        },
        cache: 'no-store'
      });
      
      // Check for HTTP error statuses
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error: ${response.status}` }));
        throw new Error(errorData.error || `HTTP error: ${response.status}`);
      }

      // Parse the response
      const data = await response.json();
      
      // Handle the error case within a successful HTTP response
      if (data.error) {
        throw new Error(data.error);
      }
      
      console.log(`Received ${Array.isArray(data) ? data.length : 0} partnerships`);
      setPartnerships(Array.isArray(data) ? data : []);
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error("Failed to fetch partnerships:", error);
      
      if (retryCount < 3) {
        // Retry logic with exponential backoff
        const waitTime = Math.pow(2, retryCount) * 1000;
        setRetryCount(prev => prev + 1);
        
        toast.info(`Retrying partnership fetch (attempt ${retryCount + 1})...`);
        
        setTimeout(() => {
          fetchPartnerships();
        }, waitTime);
        
        return; // Don't update error state or loading state yet
      }
      
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An unknown error occurred");
      }
      toast.error("Failed to load partnerships");
    } finally {
      setLoading(false);
    }
  }, [status, admin, adminLoading, retryCount]);

  const refreshPartnerships = useCallback(() => {
    fetchedRef.current = false;
    setRetryCount(0); // Reset retry count on manual refresh
    fetchPartnerships();
  }, [fetchPartnerships]);

  useEffect(() => {
    let isMounted = true;
    
    // Only fetch once per component mount or on refresh
    if (fetchedRef.current) return;
    
    // Don't fetch if admin status is still loading
    if (adminLoading) return;
    
    fetchedRef.current = true;
    fetchPartnerships();
    
    return () => {
      isMounted = false;
    };
  }, [status, admin, adminLoading, fetchPartnerships]); 

  return { partnerships, loading, error, refreshPartnerships };
} 