"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

interface PartnershipApplication {
  id: string;
  userId: string;
  status: string;
  // Add other fields as needed
}

interface FetchResponse {
  application: PartnershipApplication | null;
  isLoading: boolean;
  error: string | null;
  mutate: () => void;
}

export function usePartnershipStatus(userId: string | undefined): FetchResponse {
  const [application, setApplication] = useState<PartnershipApplication | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!userId) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/partnership/status?userId=${userId}`);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error: ${response.status}` }));
        throw new Error(errorData.error || `Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      }
      
      setApplication(data.application);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error("Failed to fetch partnership status:", err);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Initial fetch
  useEffect(() => {
    if (userId) {
      fetchData();
    }
  }, [userId, fetchData]);

  // Manual refetch function
  const mutate = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    application,
    isLoading,
    error,
    mutate,
  };
}
