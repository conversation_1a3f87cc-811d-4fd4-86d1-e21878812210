import { useState } from "react";

interface ApplicationData {
  userId: string;
  username: string;
  companyName: string;
  websiteUrl: string;
  reason: string;
  experience: string;
  additional?: string;
}

interface ApplicationResponse {
  message?: string;
  error?: string;
  application?: any;
}

export function usePartnershipApplication() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const applyForPartnership = async (data: ApplicationData) => {
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch("/api/partnership/post", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": `${process.env.NEXT_PUBLIC_API_KEY}`,
        },
        body: JSON.stringify(data),
      });

      const result: ApplicationResponse = await response.json();

      if (!response.ok) throw new Error(result.error || "Something went wrong");

      setSuccessMessage(result.message || "Application submitted!");
      return result.application;
    } catch (err: any) {
      setError(err.message || "Failed to submit application.");
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { applyForPartnership, loading, error, successMessage };
}
