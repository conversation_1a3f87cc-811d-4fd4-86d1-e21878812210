"use client";
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

interface UseAdminResult {
  admin: boolean;
  loading: boolean;
  error: string | null;
}

/**
 * Hook to check if the current user has admin privileges
 * Now relies primarily on middleware.ts for access control
 * This hook is used for UI elements that need to know the admin status
 * If the user is not an admin, the middleware will handle the redirect
 */
export default function useAdmin(): UseAdminResult {
  const { data: session, status } = useSession();
  const [admin, setAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setError(null);
    
    if (status === 'loading') return;

    const email = session?.user?.email;
    if (!email) {
      setLoading(false);
      return;
    }

    setLoading(true);
    
    const checkAdminStatus = async () => {
      try {
        const res = await fetch('/api/admin-check', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email })
        });

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({ error: `HTTP error: ${res.status}` }));
          throw new Error(errorData.error || `HTTP error: ${res.status}`);
        }

        const data = await res.json();
        setAdmin(data.admin === true);
      } catch (err) {
        console.error("Admin check failed:", err);
        const errorMessage = err instanceof Error ? err.message : "Unknown error.";
        setError(errorMessage);
        setAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [session, status]);

  return { admin, loading, error };
}