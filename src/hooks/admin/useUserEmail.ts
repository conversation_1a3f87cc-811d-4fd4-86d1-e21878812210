"use client";

import { useState } from 'react';
import { toast } from 'sonner';
import { User } from './useUserManagement';

interface EmailPayload {
  email: string;
  title: string;
  description: string;
  footer?: string;
}

export function useSendEmail() {
  const [sending, setSending] = useState<boolean>(false);
  const [recipientCount, setRecipientCount] = useState<number>(0);

  // Send email to multiple recipients
  const sendBulkEmail = async (
    recipients: User[] | "all", 
    title: string, 
    description: string, 
    footer?: string
  ) => {
    try {
      setSending(true);
      
      // If recipients is "all", we'll handle it in the API
      if (recipients === "all") {
        const response = await fetch('/api/admin/email/bulk', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.NEXT_PUBLIC_API_KEY || ''
          },
          body: JSON.stringify({
            allUsers: true,
            title,
            description,
            footer
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to send emails');
        }

        const result = await response.json();
        setRecipientCount(result.sentCount || 0);
        toast.success(`Email sent to all users (${result.sentCount} recipients)`);
        return { success: true, count: result.sentCount };
      } 
      // Otherwise, send to specific users
      else {
        // Extract email addresses
        const emails = recipients.map(user => user.email).filter(Boolean) as string[];
        
        if (emails.length === 0) {
          toast.error('No valid email addresses found');
          return { success: false, count: 0 };
        }

        const response = await fetch('/api/admin/email/bulk', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.NEXT_PUBLIC_API_KEY || ''
          },
          body: JSON.stringify({
            emails,
            title,
            description,
            footer
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to send emails');
        }

        const result = await response.json();
        setRecipientCount(result.sentCount || 0);
        toast.success(`Email sent to ${emails.length} recipients`);
        return { success: true, count: emails.length };
      }
    } catch (error) {
      console.error('Error sending emails:', error);
      const message = error instanceof Error ? error.message : 'Failed to send emails';
      toast.error(message);
      return { success: false, count: 0 };
    } finally {
      setSending(false);
    }
  };

  // Send email to a single recipient
  const sendEmail = async (email: string, title: string, description: string, footer?: string) => {
    try {
      setSending(true);

      const response = await fetch('/api/mail/sender', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_API_KEY || ''
        },
        body: JSON.stringify({
          email,
          title,
          description,
          footer
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send email');
      }

      toast.success('Email sent successfully');
      return { success: true };
    } catch (error) {
      console.error('Error sending email:', error);
      const message = error instanceof Error ? error.message : 'Failed to send email';
      toast.error(message);
      return { success: false };
    } finally {
      setSending(false);
    }
  };

  return { sendEmail, sendBulkEmail, sending, recipientCount };
} 