"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

// Define types for admin statistics
export interface AdminStats {
  counts: {
    users: number;
    admins: number;
    pendingStaffApplications: number;
    approvedStaffApplications: number;
    pendingPartnershipApplications: number;
    approvedPartnershipApplications: number;
    totalApps: number;
    pendingApps: number;
    approvedApps: number;
    rejectedApps: number;
    suspendedApps: number;
  };
  recentActivity: {
    users: Array<{
      id: string;
      name: string | null;
      email: string | null;
      image: string | null;
      admin: boolean;
    }>;
    staffApplications: Array<{
      id: string;
      fullName: string;
      position: string;
      status: string;
      appliedAt: string;
      userId: string;
      email: string | null;
    }>;
    partnershipApplications: Array<{
      id: string;
      username: string;
      companyName: string;
      status: string;
      appliedAt: string;
      userId: string;
      email: string | null;
    }>;
  };
}

interface UseAdminStatsResult {
  stats: AdminStats | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export default function useAdminStats(): UseAdminStatsResult {
  const { data: session, status } = useSession();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    if (status !== 'authenticated') {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/stats');

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP error: ${response.status}` }));
        throw new Error(errorData.error || `HTTP error: ${response.status}`);
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error("Admin stats fetch failed:", err);
      const errorMessage = err instanceof Error ? err.message : "Unknown error.";
      setError(errorMessage);
      toast.error(`Failed to load admin statistics: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchStats();
    }
  }, [session, status]);

  return { stats, loading, error, refetch: fetchStats };
}