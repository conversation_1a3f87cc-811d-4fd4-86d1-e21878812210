"use client";

import { useState } from 'react';
import { toast } from 'sonner';

// Interface for User
export interface User {
  id: string;
  name: string | null;
  email: string | null;
  image: string | null;
  admin: boolean;
  isDeveloper: boolean;
}

// Hook for updating users
export function useUpdateUser() {
  const [updating, setUpdating] = useState<boolean>(false);
  const [updatingId, setUpdatingId] = useState<string | null>(null);

  const updateUser = async (id: string, data: Partial<User>) => {
    try {
      setUpdating(true);
      setUpdatingId(id);

      const response = await fetch(`/api/users/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update user');
      }

      const updatedUser = await response.json();
      toast.success('User updated successfully');
      return updatedUser;
    } catch (error) {
      console.error('Error updating user:', error);
      const message = error instanceof Error ? error.message : 'Failed to update user';
      toast.error(message);
      return null;
    } finally {
      setUpdating(false);
      setUpdatingId(null);
    }
  };

  return { updateUser, updating, updatingId };
}

// Hook for deleting users
export function useDeleteUser() {
  const [deleting, setDeleting] = useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const deleteUser = async (id: string) => {
    try {
      setDeleting(true);
      setDeletingId(id);

      const response = await fetch(`/api/users/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete user');
      }

      toast.success('User deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      const message = error instanceof Error ? error.message : 'Failed to delete user';
      toast.error(message);
      return false;
    } finally {
      setDeleting(false);
      setDeletingId(null);
    }
  };

  return { deleteUser, deleting, deletingId };
}