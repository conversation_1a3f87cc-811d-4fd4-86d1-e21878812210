/*
 * Admin Apps Hook - Custom hook for admin app management operations
 */

import { useState } from "react";
import { toast } from "sonner";

// App interface
interface App {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  tags: string[];
  downloads: number;
  status: "PENDING" | "APPROVED" | "REJECTED" | "SUSPENDED";
  createdAt: string;
  updatedAt: string;
  iconUrl?: string;
  bannerUrl?: string;
  screenshots: string[];
  developer: {
    id: string;
    name: string;
    image?: string;
  };
  _count: {
    comments: number;
  };
}

export function useAdminApps() {
  const [loading, setLoading] = useState(false);
  const [processingId, setProcessingId] = useState<string | null>(null);

  // Fetch all apps for admin
  const fetchApps = async (filters?: {
    search?: string;
    status?: string;
    category?: string;
  }) => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        admin: "true" // Include all statuses for admin
      });

      if (filters?.search) {
        params.append("search", filters.search);
      }
      if (filters?.status && filters.status !== "ALL") {
        params.append("status", filters.status);
      }
      if (filters?.category) {
        params.append("category", filters.category);
      }

      const response = await fetch(`/api/apps?${params}`);
      
      if (response.ok) {
        const apps = await response.json();
        return apps;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to fetch apps");
        return [];
      }
    } catch (error) {
      console.error("Error fetching apps:", error);
      toast.error("Failed to fetch apps");
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Update app status
  const updateAppStatus = async (
    appId: string, 
    newStatus: "APPROVED" | "REJECTED" | "SUSPENDED",
    reason?: string
  ) => {
    try {
      setProcessingId(appId);
      
      const response = await fetch(`/api/admin/apps/${appId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ 
          status: newStatus,
          reason: reason || `App ${newStatus.toLowerCase()} by admin`
        })
      });

      if (response.ok) {
        const updatedApp = await response.json();
        toast.success(`App ${newStatus.toLowerCase()} successfully`);
        return updatedApp;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update app status");
        return null;
      }
    } catch (error) {
      console.error("Error updating app status:", error);
      toast.error("Failed to update app status");
      return null;
    } finally {
      setProcessingId(null);
    }
  };

  // Get app details
  const getAppDetails = async (appId: string) => {
    try {
      const response = await fetch(`/api/admin/apps/${appId}`);
      
      if (response.ok) {
        const app = await response.json();
        return app;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to fetch app details");
        return null;
      }
    } catch (error) {
      console.error("Error fetching app details:", error);
      toast.error("Failed to fetch app details");
      return null;
    }
  };

  // Delete app
  const deleteApp = async (appId: string) => {
    try {
      setProcessingId(appId);
      
      const response = await fetch(`/api/admin/apps/${appId}`, {
        method: "DELETE"
      });

      if (response.ok) {
        toast.success("App deleted successfully");
        return true;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete app");
        return false;
      }
    } catch (error) {
      console.error("Error deleting app:", error);
      toast.error("Failed to delete app");
      return false;
    } finally {
      setProcessingId(null);
    }
  };

  // Bulk operations
  const bulkUpdateStatus = async (
    appIds: string[],
    newStatus: "APPROVED" | "REJECTED" | "SUSPENDED",
    reason?: string
  ) => {
    try {
      setLoading(true);
      
      const results = await Promise.allSettled(
        appIds.map(appId => updateAppStatus(appId, newStatus, reason))
      );

      const successful = results.filter(result => 
        result.status === "fulfilled" && result.value !== null
      ).length;

      const failed = results.length - successful;

      if (successful > 0) {
        toast.success(`${successful} app(s) ${newStatus.toLowerCase()} successfully`);
      }
      if (failed > 0) {
        toast.error(`Failed to update ${failed} app(s)`);
      }

      return { successful, failed };
    } catch (error) {
      console.error("Error in bulk update:", error);
      toast.error("Bulk update failed");
      return { successful: 0, failed: appIds.length };
    } finally {
      setLoading(false);
    }
  };

  // Get app statistics
  const getAppStats = async () => {
    try {
      const apps = await fetchApps();
      
      const stats = {
        total: apps.length,
        pending: apps.filter((app: App) => app.status === "PENDING").length,
        approved: apps.filter((app: App) => app.status === "APPROVED").length,
        rejected: apps.filter((app: App) => app.status === "REJECTED").length,
        suspended: apps.filter((app: App) => app.status === "SUSPENDED").length,
        totalDownloads: apps.reduce((sum: number, app: App) => sum + app.downloads, 0),
        totalComments: apps.reduce((sum: number, app: App) => sum + app._count.comments, 0)
      };

      return stats;
    } catch (error) {
      console.error("Error fetching app stats:", error);
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        suspended: 0,
        totalDownloads: 0,
        totalComments: 0
      };
    }
  };

  return {
    loading,
    processingId,
    fetchApps,
    updateAppStatus,
    getAppDetails,
    deleteApp,
    bulkUpdateStatus,
    getAppStats
  };
}

export default useAdminApps;
