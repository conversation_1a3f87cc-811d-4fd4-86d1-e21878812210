/*
 * Official Avehub Code, verified
 * Admin Toggle Hook - Optimized for performance with optimistic updates
 * Any unauthorized modifications will invalidate service warranty
 */

"use client"; 
import { useState, useRef } from "react";
import { toast } from "sonner";

// [1] Hook to handle admin status toggling with optimistic updates
export function useToggleAdmin() {
  // [1.1] Track loading state per user ID
  const [loadingUsers, setLoadingUsers] = useState<Record<string, boolean>>({});
  // [1.2] Track timeouts for loading indicators
  const timeoutRefs = useRef<Record<string, NodeJS.Timeout>>({});
  
  // [1.3] Check if a specific user is in loading state
  const isUserLoading = (userId: string): boolean => loadingUsers[userId] || false;
  
  // [1.4] Toggle admin status with optimistic updates
  const toggleAdmin = async (userId: string, currentAdminState: boolean): Promise<boolean> => {
    // Prevent multiple calls for the same user
    if (loadingUsers[userId]) return currentAdminState;
    
    // Set loading state optimistically
    setLoadingUsers(prev => ({ ...prev, [userId]: true }));
    
    // Show loading indicator only if request takes longer than 500ms
    timeoutRefs.current[userId] = setTimeout(() => {
      toast.loading(`Processing admin status change...`, { id: `admin-toggle-${userId}` });
    }, 500);
    
    try {
      // Call API to toggle admin status
      const res = await fetch("/api/admin/toggle/adminstatus", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userId }),
      });
      
      // Clear timeout to prevent showing toast if request was fast
      clearTimeout(timeoutRefs.current[userId]);
      
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || "Failed to toggle admin status");
      }
      
      const data = await res.json();
      
      // Show success toast
      toast.success(`User admin status updated`, { id: `admin-toggle-${userId}` });
      
      // Remove loading state
      setLoadingUsers(prev => ({ ...prev, [userId]: false }));
      
      // Return new admin state from server
      return data.admin;
    } catch (error) {
      // Clear timeout
      clearTimeout(timeoutRefs.current[userId]);
      
      // Show error toast
      toast.error(`Failed to update admin status: ${error instanceof Error ? error.message : "Unknown error"}`, 
        { id: `admin-toggle-${userId}` });
      
      // Remove loading state
      setLoadingUsers(prev => ({ ...prev, [userId]: false }));
      
      // Return original state on error
      return currentAdminState;
    }
  };
  
  return { toggleAdmin, isUserLoading };
}

export default useToggleAdmin;
