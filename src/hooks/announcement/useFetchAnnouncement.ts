import { useState, useEffect } from "react";

interface Announcement {
  id: string;
  title: string;
  description: string;
  footer: string;
  imageUrl?: string;
}

export function useFetchAnnouncements() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        const response = await fetch(`/api/announcements`, {
          method: "GET",
        });

        const text = await response.text();
        console.log("Raw response text:", text);

        if (!text) {
          throw new Error("Empty response from the announcements API");
        }

        const data = JSON.parse(text);

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch announcements");
        }

        setAnnouncements(data);
        setCurrentIndex(0);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchAnnouncements();
  }, []);

  const goToNext = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : prev));
  };

  const goToPrev = () => {
    setCurrentIndex((prev) => (prev < announcements.length - 1 ? prev + 1 : prev));
  };

  return {
    announcement: announcements[currentIndex] || null,
    announcements,
    currentIndex,
    goToPrev,
    goToNext,
    loading,
    error
  };
}
