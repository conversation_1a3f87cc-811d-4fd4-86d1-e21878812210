import { useState } from "react";

export function useDeleteAnnouncement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteAnnouncement = async (id: string, onSuccess?: () => void) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/announcements/delete?id=${id}`, {
        method: "DELETE",
        headers: {
          "x-api-key": `${process.env.NEXT_PUBLIC_API_KEY}`,
        },
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || "Failed to delete announcement");
      }
      if (onSuccess) onSuccess();
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return { deleteAnnouncement, loading, error };
}
