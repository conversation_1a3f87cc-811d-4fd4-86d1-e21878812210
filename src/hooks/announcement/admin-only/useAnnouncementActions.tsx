import { useState } from "react";

interface AnnouncementData {
  title: string;
  description: string;
  footer: string;
}

export function useCreateAnnouncement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createAnnouncement = async (data: AnnouncementData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/announcements/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": `${process.env.NEXT_PUBLIC_API_KEY}`,
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      if (!response.ok) throw new Error(result.error || "Failed to create announcement");

      return result.announcement;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      }
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { createAnnouncement, loading, error };
}
