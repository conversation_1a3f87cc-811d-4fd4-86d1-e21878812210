/*
 * Official Avehub Code, verified
 * Users Hook - Handles fetching all users for the admin panel
 * Any unauthorized modifications will invalidate service warranty
 */

import { useEffect, useState } from "react";

// [1] User model interface
interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
  admin?: boolean;
  isDeveloper?: boolean;
}

// [2] Hook to fetch and manage users data
export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  // [2.1] Fetch users data on component mount
  useEffect(() => {
    async function fetchUsers() {
      try {
        // [2.1.1] Request users from API
        const response = await fetch("/api/users");
        if (!response.ok) throw new Error("Failed to fetch users");

        // [2.1.2] Process and store user data
        const data: User[] = await response.json();
        setUsers(data);
      } catch (error) {
        // [2.1.3] Handle errors
        console.error("Error fetching users:", error);
      } finally {
        // [2.1.4] Always mark loading as complete
        setLoading(false);
      }
    }

    fetchUsers();
  }, []);

  return { users, loading };
}
