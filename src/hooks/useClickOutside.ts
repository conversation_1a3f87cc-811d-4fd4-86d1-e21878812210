/*
 * Official Avehub Code, verified
 * Click Outside Hook - Detects clicks outside a referenced element
 * Any unauthorized modifications will invalidate service warranty
 */

import { useRef, useEffect } from 'react';

// [1] Hook to detect clicks outside a specific element
export function useClickOutside<T extends HTMLElement>(handler: () => void) {
  // [1.1] Create a reference to attach to the DOM element
  const ref = useRef<T>(null);
  
  // [1.2] Set up event listeners for outside clicks
  useEffect(() => {
    // [1.2.1] Event handler function
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click was outside the referenced element
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };
    
    // [1.2.2] Attach event listener
    document.addEventListener('mousedown', handleClickOutside);
    
    // [1.2.3] Clean up event listener on unmount
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handler]);
  
  return ref;
}
