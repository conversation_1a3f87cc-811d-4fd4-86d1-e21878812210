/*
 * Official Avehub Code, verified
 * Apps Hook - Custom hook for app management
 * Any unauthorized modifications will invalidate service warranty
 */

import { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  App,
  AppListing,
  AppFilters,
  CreateAppData,
  UpdateAppData
} from "@/types/app";

// [1] Hook for fetching apps (listing)
export function useApps(filters?: AppFilters) {
  const [apps, setApps] = useState<AppListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchApps = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== "") {
            params.append(key, value.toString());
          }
        });
      }

      const response = await fetch(`/api/apps?${params}`);
      if (response.ok) {
        const data = await response.json();
        setApps(data);
      } else {
        throw new Error(`Failed to fetch apps: ${response.status}`);
      }
    } catch (error) {
      console.error("Error fetching apps:", error);
      setError("Failed to load apps");
      toast.error("Failed to load apps");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApps();
  }, [
    filters?.search,
    filters?.category,
    filters?.status,
    filters?.developerId,
    filters?.sortBy,
    filters?.order,
    filters?.featured,
    filters?.limit
  ]);

  return { apps, loading, error, refetch: fetchApps };
}

// [2] Hook for fetching single app
export function useApp(id: string) {
  const [app, setApp] = useState<App | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchApp = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/apps/${id}`);
      if (response.ok) {
        const data = await response.json();
        setApp(data);
      } else if (response.status === 404) {
        setError("App not found");
        toast.error("App not found");
      } else {
        throw new Error(`Failed to fetch app: ${response.status}`);
      }
    } catch (error) {
      console.error("Error fetching app:", error);
      setError("Failed to load app");
      toast.error("Failed to load app");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApp();
  }, [id]);

  return { app, loading, error, refetch: fetchApp };
}

// [3] Hook for creating apps
export function useCreateApp() {
  const [creating, setCreating] = useState(false);

  const createApp = async (appData: CreateAppData): Promise<App | null> => {
    setCreating(true);
    try {
      const response = await fetch("/api/apps", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(appData)
      });

      if (response.ok) {
        const app: App = await response.json();
        toast.success("App created successfully!");
        return app;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to create app");
        return null;
      }
    } catch (error) {
      console.error("Error creating app:", error);
      toast.error("Failed to create app");
      return null;
    } finally {
      setCreating(false);
    }
  };

  return { createApp, creating };
}

// [4] Hook for updating apps
export function useUpdateApp() {
  const [updating, setUpdating] = useState(false);

  const updateApp = async (id: string, appData: UpdateAppData): Promise<App | null> => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/apps/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(appData)
      });

      if (response.ok) {
        const app: App = await response.json();
        toast.success("App updated successfully!");
        return app;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update app");
        return null;
      }
    } catch (error) {
      console.error("Error updating app:", error);
      toast.error("Failed to update app");
      return null;
    } finally {
      setUpdating(false);
    }
  };

  return { updateApp, updating };
}

// [5] Hook for deleting apps
export function useDeleteApp() {
  const [deleting, setDeleting] = useState(false);

  const deleteApp = async (id: string): Promise<boolean> => {
    setDeleting(true);
    try {
      const response = await fetch(`/api/apps/${id}`, {
        method: "DELETE"
      });

      if (response.ok) {
        toast.success("App deleted successfully!");
        return true;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete app");
        return false;
      }
    } catch (error) {
      console.error("Error deleting app:", error);
      toast.error("Failed to delete app");
      return false;
    } finally {
      setDeleting(false);
    }
  };

  return { deleteApp, deleting };
}
