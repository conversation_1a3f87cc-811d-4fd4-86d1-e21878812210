/*
 * Official Avehub Code, verified
 * File Upload Hook - Custom hook for handling file uploads with progress
 * Any unauthorized modifications will invalidate service warranty
 */

import { useState } from "react";
import { toast } from "sonner";

// [1] Upload result interface
export interface UploadResult {
  url: string;
  publicId: string;
  fileSize: number;
  format: string;
  width?: number;
  height?: number;
}

// [2] Upload state interface
export interface UploadState {
  uploading: boolean;
  progress: number;
  error: string | null;
}

// [3] File upload hook
export function useFileUpload() {
  const [uploadStates, setUploadStates] = useState<{ [key: string]: UploadState }>({});

  // [4] Update upload state for a specific upload
  const updateUploadState = (uploadId: string, state: Partial<UploadState>) => {
    setUploadStates(prev => ({
      ...prev,
      [uploadId]: {
        ...{
          uploading: false,
          progress: 0,
          error: null
        },
        ...prev[uploadId],
        ...state
      }
    }));
  };

  // [5] Upload file function
  const uploadFile = async (
    file: File,
    uploadType: "app" | "screenshot" | "icon" | "banner",
    uploadId?: string
  ): Promise<UploadResult | null> => {
    const id = uploadId || `${uploadType}_${Date.now()}`;

    // [5.1] Validate file
    if (!file) {
      toast.error("No file selected");
      return null;
    }

    // [5.2] Validate file size
    const maxSize = uploadType === 'app' ? 100 * 1024 * 1024 : 10 * 1024 * 1024; // 100MB for apps, 10MB for images
    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024);
      toast.error(`File too large. Maximum size: ${maxSizeMB}MB`);
      return null;
    }

    // [5.3] Validate file type
    const allowedTypes = {
      app: ['.jar', '.exe', '.dmg', '.deb', '.rpm', '.zip', '.tar.gz'],
      screenshot: ['.jpg', '.jpeg', '.png', '.webp'],
      icon: ['.jpg', '.jpeg', '.png', '.webp', '.svg'],
      banner: ['.jpg', '.jpeg', '.png', '.webp']
    };

    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const validTypes = allowedTypes[uploadType];

    if (!validTypes.includes(fileExtension)) {
      toast.error(`Invalid file type. Allowed: ${validTypes.join(', ')}`);
      return null;
    }

    // [5.4] Initialize upload state
    updateUploadState(id, {
      uploading: true,
      progress: 0,
      error: null
    });

    try {
      // [5.5] Prepare form data
      const formData = new FormData();
      formData.append("file", file);
      formData.append("uploadType", uploadType);

      // [5.6] Create XMLHttpRequest for progress tracking
      const xhr = new XMLHttpRequest();

      // [5.7] Track upload progress
      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          updateUploadState(id, { progress });
        }
      });

      // [5.8] Create upload promise
      const uploadPromise = new Promise<UploadResult>((resolve, reject) => {
        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const result = JSON.parse(xhr.responseText);
              resolve(result);
            } catch (error) {
              reject(new Error("Invalid response format"));
            }
          } else {
            try {
              const error = JSON.parse(xhr.responseText);
              reject(new Error(error.error || "Upload failed"));
            } catch {
              reject(new Error("Upload failed"));
            }
          }
        };

        xhr.onerror = () => {
          reject(new Error("Network error during upload"));
        };

        xhr.ontimeout = () => {
          reject(new Error("Upload timeout"));
        };
      });

      // [5.9] Start upload
      xhr.open("POST", "/api/upload");
      xhr.timeout = 5 * 60 * 1000; // 5 minutes timeout
      xhr.send(formData);

      // [5.10] Wait for upload completion
      const result = await uploadPromise;

      // [5.11] Update state on success
      updateUploadState(id, {
        uploading: false,
        progress: 100,
        error: null
      });

      toast.success("File uploaded successfully!");
      return result;

    } catch (error) {
      // [5.12] Handle upload error
      const errorMessage = error instanceof Error ? error.message : "Upload failed";

      updateUploadState(id, {
        uploading: false,
        progress: 0,
        error: errorMessage
      });

      toast.error(errorMessage);
      return null;
    }
  };

  // [6] Upload multiple files
  const uploadMultipleFiles = async (
    files: File[],
    uploadType: "screenshot",
    onProgress?: (completed: number, total: number) => void
  ): Promise<UploadResult[]> => {
    const results: UploadResult[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const uploadId = `${uploadType}_${i}_${Date.now()}`;

      const result = await uploadFile(file, uploadType, uploadId);
      if (result) {
        results.push(result);
      }

      if (onProgress) {
        onProgress(i + 1, files.length);
      }
    }

    return results;
  };

  // [7] Get upload state for a specific upload
  const getUploadState = (uploadId: string): UploadState => {
    return uploadStates[uploadId] || {
      uploading: false,
      progress: 0,
      error: null
    };
  };

  // [8] Clear upload state
  const clearUploadState = (uploadId: string) => {
    setUploadStates(prev => {
      const newStates = { ...prev };
      delete newStates[uploadId];
      return newStates;
    });
  };

  // [9] Clear all upload states
  const clearAllUploadStates = () => {
    setUploadStates({});
  };

  return {
    uploadFile,
    uploadMultipleFiles,
    getUploadState,
    clearUploadState,
    clearAllUploadStates,
    uploadStates
  };
}
