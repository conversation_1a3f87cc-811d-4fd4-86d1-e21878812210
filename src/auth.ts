/*
 * Official Avehub Code, verified
 * Authentication Configuration - Central setup for NextAuth
 * Any unauthorized modifications will invalidate service warranty
 */

import NextAuth from "next-auth";
import { authConfig } from "@/lib/auth.config";
import { PrismaAdapter } from "@auth/prisma-adapter";
import db from "@/lib/db";

// [1] NextAuth configuration and exports
export const { handlers, signIn, signOut, auth } = NextAuth({
  ...authConfig,
  adapter: PrismaAdapter(db),

  // [1.1] Session configuration
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days - session expiration time
  },

  // [1.2] Callbacks for authentication lifecycle
  callbacks: {
    // [1.2.1] JWT callback - Customize token content and refresh user data
    async jwt({ token, user }) {
      // If this is a new sign-in, set initial token data
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.admin = user.admin === true;
        token.isDeveloper = user.isDeveloper === true;
        // Removed console.log for production
      }

      // Always fetch fresh user data from database to ensure latest permissions
      if (token.id) {
        try {
          const freshUser = await db.user.findUnique({
            where: { id: token.id as string },
            select: {
              id: true,
              email: true,
              name: true,
              admin: true,
              isDeveloper: true,
              image: true
            }
          });

          if (freshUser) {
            token.id = freshUser.id;
            token.email = freshUser.email;
            token.name = freshUser.name;
            token.admin = freshUser.admin === true;
            token.isDeveloper = freshUser.isDeveloper === true;
            token.image = freshUser.image;
            // Removed console.log for production
          }
        } catch (error) {
          console.error("Error fetching fresh user data in JWT callback:", error);
        }
      }

      return token;
    },

    // [1.2.2] Session callback - Make token data available to client
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
        session.user.admin = token.admin === true;
        session.user.isDeveloper = token.isDeveloper === true;
        session.user.image = token.image as string;
        // Removed console.log for production
      }
      return session;
    },

    // [1.2.3] Redirect callback - Handle post-authentication redirects
    redirect() {
      return "/";
    },
  }
});
