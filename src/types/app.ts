/*
 * Official Avehub Code, verified
 * App Types - Centralized TypeScript interfaces for app-related data
 * Any unauthorized modifications will invalidate service warranty
 */

// [1] Core App Status and Visibility Enums
export type AppStatus = "PENDING" | "APPROVED" | "REJECTED" | "SUSPENDED";
export type AppVisibility = "PUBLIC" | "PRIVATE" | "UNLISTED";

// [2] User interface for app relationships
export interface AppUser {
  id: string;
  name: string;
  image?: string;
}

// [3] App Version interface
export interface AppVersion {
  id: string;
  version: string;
  downloadUrl: string;
  fileSize: number;
  changelog?: string;
  createdAt: string;
}

// [4] App Comment interface
export interface AppComment {
  id: string;
  content: string;
  rating?: number;
  createdAt: string;
  updatedAt: string;
  user: AppUser;
}

// [5] Core App interface
export interface App {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  tags: string[];
  downloadUrl: string;
  fileSize: number;
  screenshots: string[];
  iconUrl?: string;
  bannerUrl?: string;
  downloads: number;
  status: AppStatus;
  visibility: AppVisibility;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  minVersion?: string;
  maxVersion?: string;
  website?: string;
  supportEmail?: string;
  changelog?: string;
  developer: AppUser;
  comments: AppComment[];
  versions: AppVersion[];
  _count: {
    comments: number;
  };
}

// [6] App listing interface (simplified for listing pages)
export interface AppListing {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  tags: string[];
  downloads: number;
  status: AppStatus;
  createdAt: string;
  updatedAt: string;
  iconUrl?: string;
  bannerUrl?: string;
  featured: boolean;
  developer: AppUser;
  _count: {
    comments: number;
  };
}

// [7] App creation/update interfaces
export interface CreateAppData {
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  tags?: string[];
  downloadUrl: string;
  fileSize: number;
  screenshots?: string[];
  iconUrl?: string;
  bannerUrl?: string;
  minVersion?: string;
  maxVersion?: string;
  website?: string;
  supportEmail?: string;
  changelog?: string;
  visibility?: AppVisibility;
}

export interface UpdateAppData extends Partial<CreateAppData> {
  status?: AppStatus;
  featured?: boolean;
}

// [8] App filters interface
export interface AppFilters {
  search?: string;
  category?: string;
  status?: AppStatus;
  developerId?: string;
  sortBy?: string;
  order?: "asc" | "desc";
  featured?: boolean;
  limit?: number;
}

// [9] Download response interface
export interface DownloadResponse {
  success: boolean;
  app: {
    id: string;
    name: string;
    version: string;
  };
  download: {
    directUrl: string;
    streamingUrl: string;
    fileName: string;
    contentType: string;
    supportsRangeRequests: boolean;
  };
  downloads: number;
  externalDownloads?: number;
  timestamp: string;
}

// [10] Comment creation interface
export interface CreateCommentData {
  content: string;
  rating?: number;
}

// [11] App categories
export const APP_CATEGORIES = [
  "All",
  "Productivity",
  "Development",
  "Games",
  "Utilities",
  "Education",
  "Business",
  "Entertainment",
  "Graphics",
  "Security",
  "Other"
] as const;

export type AppCategory = typeof APP_CATEGORIES[number];

// [12] Sort options
export const SORT_OPTIONS = [
  { value: "createdAt-desc", label: "Newest First" },
  { value: "createdAt-asc", label: "Oldest First" },
  { value: "downloads-desc", label: "Most Downloaded" },
  { value: "downloads-asc", label: "Least Downloaded" },
  { value: "name-asc", label: "Name A-Z" },
  { value: "name-desc", label: "Name Z-A" }
] as const;

// [13] View modes
export type ViewMode = "grid" | "list";

// [14] API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
