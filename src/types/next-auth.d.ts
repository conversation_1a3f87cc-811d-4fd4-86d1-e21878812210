import { DefaultSession } from "next-auth";
import { AdapterUser as OriginalAdapterUser } from "@auth/core/adapters";

declare module "next-auth" {
  interface User {
    id: string;
    admin: boolean;
    isDeveloper: boolean;
  }

  interface Session {
    user: {
      id: string;
      email?: string;
      name?: string;
      admin: boolean;
      isDeveloper: boolean;
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    admin: boolean;
    isDeveloper: boolean;
  }
}

declare module "@auth/core/adapters" {
  interface AdapterUser extends OriginalAdapterUser {
    admin: boolean;
    isDeveloper: boolean;
  }
}