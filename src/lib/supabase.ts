import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client with error handling
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Please add NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY to your .env file');
}

// Create client with defensive fallback for server components
let supabase: SupabaseClient | null = null;

// Init function to ensure bucket exists - can be called before operations
export async function initSupabaseStorage(): Promise<boolean> {
  if (!supabase || !supabaseUrl || !supabaseServiceKey) return false;
  
  try {
    // Check if plugins bucket exists
    const { data: buckets } = await supabase.storage.listBuckets();
    const pluginsBucket = buckets?.find((b: { name: string }) => b.name === 'plugins');
    
    // Create bucket if it doesn't exist
    if (!pluginsBucket) {
      const { error } = await supabase.storage.createBucket('plugins', {
        public: true
      });
      
      if (error) {
        console.error('Failed to create plugins bucket:', error);
        return false;
      }
      
      console.log('Created plugins bucket in Supabase storage');
    }
    
    return true;
  } catch (error) {
    console.error('Error initializing Supabase storage:', error);
    return false;
  }
}

try {
  supabase = createClient(
    supabaseUrl || 'https://placeholder.supabase.co', 
    supabaseServiceKey || 'placeholder',
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    }
  );
} catch (error) {
  console.error('Error initializing Supabase client:', error);
}

export default supabase;

// Helper function to upload a file to Supabase storage
export async function uploadPluginFile(file: ArrayBuffer, filename: string): Promise<string> {
  if (!supabase || !supabaseUrl || !supabaseServiceKey) {
    throw new Error('Supabase client not properly initialized. Check environment variables.');
  }
  
  try {
    // Ensure the bucket exists
    await initSupabaseStorage();
    
    // Ensure the public folder exists by trying to list it
    try {
      await supabase.storage.from('plugins').list('public');
    } catch (error) {
      // If listing fails, try to create an empty file to initialize the folder
      await supabase.storage
        .from('plugins')
        .upload('public/.folder', new Uint8Array(0), {
          upsert: true,
        });
    }
    
    // Now upload the actual file
    const { data, error } = await supabase.storage
      .from('plugins')
      .upload(`public/${filename}`, file, {
        contentType: 'application/octet-stream',
        upsert: true,
      });

    if (error) {
      console.error('Error uploading file to Supabase:', error);
      throw error;
    }

    // Return the public URL of the uploaded file
    const { data: publicURLData } = supabase.storage
      .from('plugins')
      .getPublicUrl(`public/${filename}`);

    console.log('File uploaded successfully, URL:', publicURLData.publicUrl);
    return publicURLData.publicUrl;
  } catch (error: any) {
    console.error('Error in uploadPluginFile:', error);
    throw new Error(`Failed to upload plugin file: ${error.message || error}`);
  }
}

// Helper function to delete a file from Supabase storage
export async function deletePluginFile(fileUrl: string): Promise<void> {
  if (!supabase || !supabaseUrl || !supabaseServiceKey) {
    console.warn('Supabase client not properly initialized. Skipping file deletion.');
    return;
  }
  
  try {
    // Extract the file path from the URL
    const url = new URL(fileUrl);
    const pathParts = url.pathname.split('/');
    
    // The URL format is typically: https://{project}.supabase.co/storage/v1/object/public/{bucket}/{file_path}
    const publicIndex = pathParts.indexOf('public');
    
    if (publicIndex === -1 || publicIndex + 2 >= pathParts.length) {
      console.error('Invalid Supabase URL format:', fileUrl);
      return;
    }
    
    // Skip the bucket name (which should be 'plugins') and get the rest of the path
    const filePath = pathParts.slice(publicIndex + 2).join('/');

    if (!filePath) {
      console.error('Invalid file path extracted from URL:', fileUrl);
      return;
    }

    console.log('Deleting file from Supabase:', filePath);
    
    const { error } = await supabase.storage
      .from('plugins')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting file from Supabase:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error processing delete operation:', error);
    throw error;
  }
} 