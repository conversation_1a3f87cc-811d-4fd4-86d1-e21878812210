import { Client, Storage, ID } from 'node-appwrite';

const appwriteEndpoint = process.env.APPWRITE_ENDPOINT || '';
const appwriteProject = process.env.APPWRITE_PROJECT || '';
const appwriteApiKey = process.env.APPWRITE_API_KEY || '';

export const appwriteClient = new Client();
if (appwriteEndpoint && appwriteProject && appwriteApiKey) {
  appwriteClient
    .setEndpoint(appwriteEndpoint)
    .setProject(appwriteProject)
    .setKey(appwriteApiKey);
}

export const storage = new Storage(appwriteClient);

export async function uploadLicenseScreenshot(file: File): Promise<string> {
  if (!appwriteEndpoint || !appwriteProject || !appwriteApiKey) {
    throw new Error('Appwrite not configured');
  }

  const bucketId = process.env.APPWRITE_LICENSE_BUCKET || 'licenses';
  const buffer = Buffer.from(await file.arrayBuffer());

  // Create a File object from the buffer
  const fileObject = new File([buffer], file.name, {
    type: file.type,
    lastModified: Date.now(),
  });

  const uploaded = await storage.createFile(bucketId, ID.unique(), fileObject);
  return uploaded.$id as string;
}

export function getFilePreview(id: string): string {
  const bucketId = process.env.APPWRITE_LICENSE_BUCKET || 'licenses';
  return `${appwriteEndpoint}/storage/buckets/${bucketId}/files/${id}/view?project=${appwriteProject}`;
}
