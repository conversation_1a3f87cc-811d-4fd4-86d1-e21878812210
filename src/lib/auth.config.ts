import { NextAuthConfig } from "next-auth";
import Google from "next-auth/providers/google";

export const authConfig: NextAuthConfig = {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],

  // Trust localhost and production domains
  trustHost: true,

  // Configure trusted hosts explicitly
  ...(process.env.NODE_ENV === "development" && {
    trustHost: true,
  }),

  // Base path for auth routes
  basePath: "/api/auth",
};

export default authConfig;