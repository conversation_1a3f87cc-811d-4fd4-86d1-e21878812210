"use client";
import { Header } from "@/components/Header";
import { motion } from "framer-motion";

export default function TermsOfService() {
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  };

  const terms = [
    {
      title: "1. Acceptance of Terms",
      content: `By engaging with AveHub&apos;s services, you acknowledge that you have read, understood, and agreed to these Terms of Service. If you do not agree, please refrain from using our services.`,
    },
    {
      title: "2. Services Offered",
      content: `AveHub provides the following services:`,
      list: [
        "Custom website development",
        "Discord bot creation and management",
        "Other custom digital solutions as requested",
      ],
    },
    {
      title: "3. Payment Terms",
      content: `
        A 50% upfront payment is required to initiate any project. Work will not begin until the deposit is received.
        The remaining 50% is due upon project completion. Deliverables, including code, will be handed over once payment is made.
        Ownership of project deliverables remains with AveHub until full payment is received.
      `,
    },
    {
      title: "4. Refund Policy",
      content: `Deposits are non-refundable once development begins. Partial refunds may be issued based on the work completed if a project is canceled.`,
    },
    {
      title: "5. Maintenance and Support",
      content: `Post-delivery support is included for up to 30 days to address bugs or minor adjustments. Additional support or major changes require a new agreement.`,
    },
    {
      title: "6. Contact Us",
      content: `For questions or support, reach out to us:`,
      list: [
        "Email: <EMAIL>",
        "Discord: discord.avehubs.com",
      ],
    },
  ];

  return (
    <div>
      <Header />
      <div className="min-h-screen flex flex-col items-center justify-between bg-gray-50 dark:bg-gray-900 p-8">
        <main className="w-full max-w-5xl py-20">
          <div className="text-left flex flex-col gap-8">
            <motion.h1
              className="text-4xl sm:text-6xl font-extrabold text-gray-800 dark:text-white leading-tight"
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
            >
              Terms of <span className="bg-gradient-to-r from-blue-600 to-orange-500 bg-clip-text text-transparent">Service</span>
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl text-gray-600 dark:text-gray-300"
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.2 }}
            >
              Welcome to AveHub! By accessing or using our services, you agree to these Terms of Service. Please read them carefully.
            </motion.p>

            {terms.map((section, index) => (
              <motion.section
                key={index}
                className="space-y-6"
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                transition={{ delay: 0.4 + index * 0.2 }}
              >
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white">{section.title}</h2>
                <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                  {section.content}
                </p>
                {section.list && (
                  <ul className="list-disc list-inside text-gray-600 dark:text-gray-300">
                    {section.list.map((item, idx) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                )}
              </motion.section>
            ))}

            <motion.p
              className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 mt-8"
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              transition={{ delay: 1.6 + terms.length * 0.2 }}
            >
              By using AveHub&apos;s services, you agree to these Terms of Service and join us in revolutionizing the way businesses achieve their goals.
            </motion.p>
          </div>
        </main>
      </div>
    </div>
  );
}
