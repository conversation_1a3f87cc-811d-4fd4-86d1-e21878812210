"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Check, X, Mail, Eye, Trash2, Loader2 } from "lucide-react";

import { Header } from "@/components/Header";
import { useFetchStaffApplications, StaffApplication } from "@/hooks/staff/useFetchStaffApplications";
import { useUpdateStaffStatus } from "@/hooks/staff/useUpdateStaffStatus";
import { useDeleteStaffApplication } from "@/hooks/staff/useDeleteStaffApplication";
import LoadingDots from "@/components/animations/Loading";

export default function AdminStaffManage() {
  const router = useRouter();
  const { data: session } = useSession();
  const { applications, loading, error, refreshApplications } = useFetchStaffApplications();
  const { updateStatus, sendStatusEmail, processingId } = useUpdateStaffStatus();
  const { deleteApplication, deletingId } = useDeleteStaffApplication();

  const [selectedApplication, setSelectedApplication] = useState<StaffApplication | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState("all");

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingDots />
      </div>
    );
  }

  // Filter applications based on tab selection
  const filteredApplications = () => {
    if (tabValue === "all") return applications;
    return applications.filter(app => app.status === tabValue);
  };

  const handleUpdateStatus = async (id: string, newStatus: "approved" | "rejected") => {
    try {
      // Update the application status
      const updatedApplication = await updateStatus(id, newStatus);
      
      if (updatedApplication) {
        // Send email notification to the applicant
        await sendStatusEmail(updatedApplication, newStatus);
        
        // Refresh the applications list
        refreshApplications();
        
        // Update the selected application if it's the one being viewed
        if (selectedApplication?.id === id) {
          setSelectedApplication((prevState: StaffApplication | null) => 
            prevState ? { ...prevState, status: newStatus } : null
          );
        }
      }
    } catch (error) {
      console.error("Error updating application status:", error);
    }
  };

  const handleDelete = async (id: string) => {
    const success = await deleteApplication(id);
    if (success) {
      refreshApplications();
      if (detailsOpen && selectedApplication?.id === id) {
        setDetailsOpen(false);
      }
    }
    setDeleteDialogOpen(false);
  };

  const viewApplicationDetails = (application: StaffApplication) => {
    setSelectedApplication(application);
    setDetailsOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
      case "accepted":
        return <Badge className="bg-green-600">Approved</Badge>;
      case "rejected":
        return <Badge className="bg-red-600">Rejected</Badge>;
      default:
        return <Badge className="bg-yellow-600">Pending</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <Header type="admin" />
      <div className="container mx-auto py-8 px-4">
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-2xl">Staff Applications</CardTitle>
            <CardDescription>
              Manage applications from users who want to join your staff team.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="mb-6" onValueChange={setTabValue}>
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="approved">Approved</TabsTrigger>
                <TabsTrigger value="rejected">Rejected</TabsTrigger>
              </TabsList>
            </Tabs>

            {loading ? (
              <div className="flex justify-center py-8">
                <LoadingDots />
              </div>
            ) : error ? (
              <div className="bg-red-100 dark:bg-red-900/20 p-4 rounded-md text-red-800 dark:text-red-200">
                {error}
              </div>
            ) : filteredApplications().length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No staff applications found.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Position</TableHead>
                      <TableHead>Age</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredApplications().map((application) => (
                      <TableRow key={application.id}>
                        <TableCell className="font-medium">{application.fullName}</TableCell>
                        <TableCell>{application.position}</TableCell>
                        <TableCell>{application.age}</TableCell>
                        <TableCell>{formatDate(application.createdAt)}</TableCell>
                        <TableCell>{getStatusBadge(application.status)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => viewApplicationDetails(application)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            
                            {application.status === "pending" && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="bg-green-600 hover:bg-green-700 text-white"
                                  onClick={() => handleUpdateStatus(application.id, "approved")}
                                  disabled={processingId === application.id}
                                >
                                  {processingId === application.id ? (
                                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                  ) : (
                                    <Check className="h-4 w-4 mr-1" />
                                  )}
                                  Approve
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="bg-red-600 hover:bg-red-700 text-white"
                                  onClick={() => handleUpdateStatus(application.id, "rejected")}
                                  disabled={processingId === application.id}
                                >
                                  {processingId === application.id ? (
                                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                  ) : (
                                    <X className="h-4 w-4 mr-1" />
                                  )}
                                  Reject
                                </Button>
                              </>
                            )}
                            
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => {
                                setSelectedApplication(application);
                                setDeleteDialogOpen(true);
                              }}
                              disabled={deletingId === application.id}
                            >
                              {deletingId === application.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Application Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Staff Application Details</DialogTitle>
            <DialogDescription>
              Review the complete application information
            </DialogDescription>
          </DialogHeader>
          
          {selectedApplication && (
            <div className="space-y-4 my-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">{selectedApplication.fullName}</h3>
                {getStatusBadge(selectedApplication.status)}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Position</div>
                  <div>{selectedApplication.position}</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Age</div>
                  <div>{selectedApplication.age}</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Location</div>
                  <div>{selectedApplication.location}</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Availability</div>
                  <div>{selectedApplication.availability}</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Email</div>
                  <div className="flex items-center">
                    <span className="mr-2">{selectedApplication.email}</span>
                    <Button variant="outline" size="sm" onClick={() => window.location.href = `mailto:${selectedApplication.email}`}>
                      <Mail className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Applied On</div>
                  <div>{formatDate(selectedApplication.createdAt)}</div>
                </div>
              </div>
              
              <div className="space-y-2 border-t pt-4">
                <div className="text-sm font-medium text-muted-foreground">Experience</div>
                <div className="p-3 bg-muted rounded-md whitespace-pre-wrap">
                  {selectedApplication.experience}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Skills</div>
                <div className="p-3 bg-muted rounded-md whitespace-pre-wrap">
                  {selectedApplication.skills}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Motivation</div>
                <div className="p-3 bg-muted rounded-md whitespace-pre-wrap">
                  {selectedApplication.motivation}
                </div>
              </div>
              
              {selectedApplication.portfolioUrl && (
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Portfolio URL</div>
                  <div>
                    <a 
                      href={selectedApplication.portfolioUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {selectedApplication.portfolioUrl}
                    </a>
                  </div>
                </div>
              )}
              
              {selectedApplication.additionalInfo && (
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Additional Information</div>
                  <div className="p-3 bg-muted rounded-md whitespace-pre-wrap">
                    {selectedApplication.additionalInfo}
                  </div>
                </div>
              )}
              
              <DialogFooter className="flex justify-between mt-6 pt-4 border-t">
                <Button
                  variant="outline"
                  className="text-red-600 hover:text-red-700"
                  onClick={() => {
                    setDetailsOpen(false);
                    setDeleteDialogOpen(true);
                  }}
                >
                  Delete Application
                </Button>
                
                {selectedApplication.status === "pending" && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="bg-green-600 hover:bg-green-700 text-white"
                      onClick={() => handleUpdateStatus(selectedApplication.id, "approved")}
                      disabled={processingId === selectedApplication.id}
                    >
                      {processingId === selectedApplication.id ? (
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        <Check className="h-4 w-4 mr-1" />
                      )}
                      Approve
                    </Button>
                    <Button
                      variant="outline"
                      className="bg-red-600 hover:bg-red-700 text-white"
                      onClick={() => handleUpdateStatus(selectedApplication.id, "rejected")}
                      disabled={processingId === selectedApplication.id}
                    >
                      {processingId === selectedApplication.id ? (
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        <X className="h-4 w-4 mr-1" />
                      )}
                      Reject
                    </Button>
                  </div>
                )}
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the staff application from {selectedApplication?.fullName}. 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={() => selectedApplication && handleDelete(selectedApplication.id)}
              disabled={deletingId === selectedApplication?.id}
            >
              {deletingId === selectedApplication?.id ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : (
                <>Delete</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 
