"use client";

import { useState, useC<PERSON>back, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Mail, CheckCircle, XCircle, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { useFetchPartnerships, Partnership } from "@/hooks/partnership/useFetchPartnerships";
import { useUpdatePartnershipStatus } from "@/hooks/partnership/useUpdatePartnershipStatus";
import { useDeletePartnership } from "@/hooks/partnership/useDeletePartnership";
import { Header } from "@/components/Header";
import LoadingDots from "@/components/animations/Loading";

export default function PartnershipManagePage() {
  const { status } = useSession();
  const router = useRouter();
  const { partnerships, loading: dataLoading, error, refreshPartnerships } = useFetchPartnerships();
  const { updateStatus, sendStatusEmail, processingId } = useUpdatePartnershipStatus();
  const { deletePartnership, deletingId } = useDeletePartnership();
  const [selectedPartnership, setSelectedPartnership] = useState<Partnership | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [partnershipToDelete, setPartnershipToDelete] = useState<Partnership | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  
  // Filter partnerships based on active tab
  const filteredPartnerships = useCallback(() => {
    if (activeTab === "all") return partnerships;
    if (activeTab === "pending") return partnerships.filter(p => p.status === "pending");
    if (activeTab === "approved") return partnerships.filter(p => p.status === "approved" || p.status === "accepted");
    if (activeTab === "rejected") return partnerships.filter(p => p.status === "rejected");
    return partnerships;
  }, [partnerships, activeTab]);

  // Only show loading screen on initial load
  useEffect(() => {
    if (!dataLoading && status !== "loading") {
      setInitialLoading(false);
    }
  }, [dataLoading, status]);

  const updatePartnershipStatus = useCallback(async (id: string, status: "approved" | "rejected") => {
    const partnership = partnerships.find(p => p.id === id);
    if (!partnership) return;

    const updatedPartnership = await updateStatus(id, status);
    if (updatedPartnership) {
      // Send email notification
      await sendStatusEmail(partnership, status);
      setDialogOpen(false);
      refreshPartnerships();
    }
  }, [partnerships, updateStatus, sendStatusEmail, refreshPartnerships]);

  const viewPartnershipDetails = useCallback((partnership: Partnership) => {
    setSelectedPartnership(partnership);
    setDialogOpen(true);
  }, []);

  const confirmDeletePartnership = useCallback((partnership: Partnership) => {
    setPartnershipToDelete(partnership);
    setDeleteDialogOpen(true);
  }, []);

  const handleDeletePartnership = useCallback(async () => {
    if (!partnershipToDelete) return;
    
    const success = await deletePartnership(partnershipToDelete.id);
    if (success) {
      setDeleteDialogOpen(false);
      // Close the detail dialog if it's the same partnership
      if (selectedPartnership?.id === partnershipToDelete.id) {
        setDialogOpen(false);
      }
      // Refresh the partnerships list
      refreshPartnerships();
    }
  }, [partnershipToDelete, deletePartnership, selectedPartnership, refreshPartnerships]);

  const getStatusBadge = useCallback((status: string) => {
    switch (status) {
      case "approved":
      case "accepted":
        return <Badge className="bg-green-500">Approved</Badge>;
      case "rejected":
        return <Badge className="bg-red-500">Rejected</Badge>;
      default:
        return <Badge className="bg-yellow-500">Pending</Badge>;
    }
  }, []);

  // Only show loading animation during initial load
  if (initialLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <LoadingDots />
      </div>
    );
  }

  return (
    <div>
      <Header type="admin" />
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Partnership Applications</CardTitle>
            <CardDescription>
              Manage partnership applications from businesses and individuals
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-red-100 text-red-600 rounded-md">
                {error}
              </div>
            )}
            
            <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="approved">Approved</TabsTrigger>
                <TabsTrigger value="rejected">Rejected</TabsTrigger>
              </TabsList>
            </Tabs>
            
            {filteredPartnerships().length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No partnership applications found</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPartnerships().map((partnership) => (
                    <TableRow key={partnership.id}>
                      <TableCell>{partnership.name}</TableCell>
                      <TableCell>{partnership.companyName}</TableCell>
                      <TableCell>{partnership.email}</TableCell>
                      <TableCell>{new Date(partnership.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>{getStatusBadge(partnership.status)}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => viewPartnershipDetails(partnership)}
                          >
                            View
                          </Button>
                          {partnership.status === "pending" && (
                            <>
                              <Button 
                                variant="default" 
                                size="sm"
                                className="bg-green-600 hover:bg-green-700"
                                onClick={() => updatePartnershipStatus(partnership.id, "approved")}
                                disabled={processingId === partnership.id}
                              >
                                {processingId === partnership.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                )}
                                Approve
                              </Button>
                              <Button 
                                variant="destructive" 
                                size="sm"
                                onClick={() => updatePartnershipStatus(partnership.id, "rejected")}
                                disabled={processingId === partnership.id}
                              >
                                {processingId === partnership.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <XCircle className="h-4 w-4 mr-1" />
                                )}
                                Reject
                              </Button>
                            </>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => confirmDeletePartnership(partnership)}
                            disabled={deletingId === partnership.id}
                          >
                            {deletingId === partnership.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                          {partnership.email && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => {
                                window.location.href = `mailto:${partnership.email}`;
                              }}
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Partnership Details Dialog */}
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Partnership Application Details</DialogTitle>
              <DialogDescription>
                Review the complete application information
              </DialogDescription>
            </DialogHeader>
            {selectedPartnership && (
              <div className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-semibold">Status:</div>
                  <div>{getStatusBadge(selectedPartnership.status)}</div>
                  
                  <div className="font-semibold">Name:</div>
                  <div>{selectedPartnership.name}</div>
                  
                  <div className="font-semibold">Company:</div>
                  <div>{selectedPartnership.companyName}</div>
                  
                  {selectedPartnership.email && (
                    <>
                      <div className="font-semibold">Email:</div>
                      <div>{selectedPartnership.email}</div>
                    </>
                  )}
                  
                  <div className="font-semibold">Website:</div>
                  <div>
                    <a href={selectedPartnership.websiteUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">
                      {selectedPartnership.websiteUrl}
                    </a>
                  </div>
                  
                  <div className="font-semibold">Date:</div>
                  <div>{new Date(selectedPartnership.createdAt).toLocaleString()}</div>
                </div>
                
                <div>
                  <div className="font-semibold mb-1">Reason:</div>
                  <div className="border rounded p-3 bg-muted/50">
                    {selectedPartnership.reason}
                  </div>
                </div>

                <div>
                  <div className="font-semibold mb-1">Experience:</div>
                  <div className="border rounded p-3 bg-muted/50">
                    {selectedPartnership.experience}
                  </div>
                </div>
                
                {selectedPartnership.additional && (
                  <div>
                    <div className="font-semibold mb-1">Additional Information:</div>
                    <div className="border rounded p-3 bg-muted/50">
                      {selectedPartnership.additional}
                    </div>
                  </div>
                )}
                
                <DialogFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    className="text-red-500"
                    onClick={() => {
                      setDialogOpen(false);
                      confirmDeletePartnership(selectedPartnership);
                    }}
                    disabled={deletingId === selectedPartnership.id}
                  >
                    {deletingId === selectedPartnership.id ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-1" />
                    ) : (
                      <Trash2 className="h-4 w-4 mr-1" />
                    )}
                    Delete
                  </Button>
                  
                  {selectedPartnership.status === "pending" && (
                    <div className="flex space-x-2">
                      <Button 
                        variant="default"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={() => updatePartnershipStatus(selectedPartnership.id, "approved")}
                        disabled={processingId === selectedPartnership.id}
                      >
                        {processingId === selectedPartnership.id ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-1" />
                        ) : (
                          <CheckCircle className="h-4 w-4 mr-1" />
                        )}
                        Approve
                      </Button>
                      <Button 
                        variant="destructive"
                        onClick={() => updatePartnershipStatus(selectedPartnership.id, "rejected")}
                        disabled={processingId === selectedPartnership.id}
                      >
                        {processingId === selectedPartnership.id ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-1" />
                        ) : (
                          <XCircle className="h-4 w-4 mr-1" />
                        )}
                        Reject
                      </Button>
                    </div>
                  )}
                </DialogFooter>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure you want to delete this partnership?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the partnership
                application from the database.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleDeletePartnership}
                className="bg-red-500 hover:bg-red-700"
              >
                {deletingId === partnershipToDelete?.id ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                ) : (
                  "Delete"
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}
