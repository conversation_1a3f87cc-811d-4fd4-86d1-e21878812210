"use client";

import { useFetchAnnouncements } from "@/hooks/announcement/useFetchAnnouncement";
import { useDeleteAnnouncement } from "@/hooks/announcement/admin-only/useDeleteAnnouncement";
import { Loader2, Trash2 } from "lucide-react";
import { motion } from "framer-motion";
import { useState } from "react";
import { Header } from "@/components/Header";

export default function ManageAnnouncements() {
  const { announcements, loading, error } = useFetchAnnouncements();
  const { deleteAnnouncement, loading: deleteLoading } = useDeleteAnnouncement();
  const [deletingId, setDeletingId] = useState<string | null>(null);

  return (
    <div>
      <Header type="admin" />
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-gray-900 to-black p-4 md:p-6">
      <div className="w-full max-w-3xl p-6 md:p-8 bg-white/10 backdrop-blur-lg shadow-xl rounded-xl border border-gray-700 transition-all duration-300">
        <h1 className="text-3xl md:text-4xl font-bold text-center text-white mb-4 md:mb-6">📢 Manage Announcements</h1>

        {loading ? (
          <div className="flex flex-col items-center text-gray-300">
            <Loader2 className="animate-spin h-6 w-6 mb-2" />
            <p>Loading announcements...</p>
          </div>
        ) : error ? (
          <p className="text-red-400 text-center bg-red-900 p-3 rounded-lg animate-pulse">{error}</p>
        ) : announcements.length === 0 ? (
          <p className="text-gray-400 text-center">No announcements found.</p>
        ) : (
          <motion.ul className="space-y-4">
            {announcements.map((announcement) => (
              <motion.li
                key={announcement.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="p-4 md:p-6 bg-gray-800 rounded-lg shadow-md flex flex-col md:flex-row justify-between items-center border border-gray-700 hover:bg-gray-700 transition-all duration-300"
              >
                <div className="flex-1 text-center md:text-left">
                  <h2 className="text-lg md:text-xl font-semibold text-white">{announcement.title}</h2>
                </div>
                <button
                  className="flex items-center gap-2 bg-red-600 text-white px-3 py-2 md:px-4 md:py-2 rounded-lg shadow-md hover:bg-red-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed mt-3 md:mt-0"
                  onClick={() => {
                    setDeletingId(announcement.id);
                    deleteAnnouncement(announcement.id, () => setDeletingId(null));
                  }}
                  disabled={deleteLoading && deletingId === announcement.id}
                >
                  {deleteLoading && deletingId === announcement.id ? (
                    <>
                      <Loader2 className="animate-spin h-5 w-5" /> Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-5 w-5" /> Delete
                    </>
                  )}
                </button>
              </motion.li>
            ))}
          </motion.ul>
        )}
      </div>
    </div>
    </div>
  );
}