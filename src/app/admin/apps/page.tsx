"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Header } from "@/components/Header";
import { motion } from "framer-motion";
import {
  Package,
  Search,
  Filter,
  Eye,
  Check,
  X,
  Pause,
  Download,
  MessageSquare,
  Calendar,
  User,
  AlertCircle,
  CheckCircle,
  XCircle,
  PauseCircle,
  Clock,
  Trash2
} from "lucide-react";
import Image from "next/image";
import { toast } from "sonner";
import LoadingDots from "@/components/animations/Loading";

// App interface
interface App {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  tags: string[];
  downloads: number;
  status: "PENDING" | "APPROVED" | "REJECTED" | "SUSPENDED";
  createdAt: string;
  updatedAt: string;
  iconUrl?: string;
  bannerUrl?: string;
  screenshots: string[];
  developer: {
    id: string;
    name: string;
    image?: string;
  };
  _count: {
    comments: number;
  };
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  APPROVED: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  REJECTED: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  SUSPENDED: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
};

const statusIcons = {
  PENDING: Clock,
  APPROVED: CheckCircle,
  REJECTED: XCircle,
  SUSPENDED: PauseCircle
};

export default function AdminAppsManagement() {
  const { data: session } = useSession();
  const router = useRouter();
  const [apps, setApps] = useState<App[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [deletingAppId, setDeletingAppId] = useState<string | null>(null);

  // Check admin access
  useEffect(() => {
    if (session && !session.user?.admin) {
      router.push("/");
    }
  }, [session, router]);

  // Fetch apps
  useEffect(() => {
    fetchApps();
  }, [searchQuery, statusFilter]);

  const fetchApps = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        search: searchQuery,
        admin: "true" // Include all statuses for admin
      });

      // Only add status filter if it's not "ALL" (let API default to "ALL" for admin)
      if (statusFilter && statusFilter !== "ALL") {
        params.append("status", statusFilter);
      }

      const response = await fetch(`/api/apps?${params}`);
      if (response.ok) {
        const data = await response.json();
        setApps(data);
      }
    } catch (error) {
      console.error("Error fetching apps:", error);
      toast.error("Failed to fetch apps");
    } finally {
      setLoading(false);
    }
  };

  // Update app status
  const updateAppStatus = async (appId: string, newStatus: "APPROVED" | "REJECTED" | "SUSPENDED") => {
    try {
      setProcessingId(appId);

      const response = await fetch(`/api/admin/apps/${appId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (response.ok) {
        const updatedApp = await response.json();
        setApps(apps.map(app => app.id === appId ? updatedApp : app));
        toast.success(`App ${newStatus.toLowerCase()} successfully`);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update app status");
      }
    } catch (error) {
      console.error("Error updating app status:", error);
      toast.error("Failed to update app status");
    } finally {
      setProcessingId(null);
    }
  };

  // Delete app function
  const handleDeleteApp = async (appId: string, appName: string) => {
    const confirmed = window.confirm(`Are you sure you want to delete "${appName}"? This action cannot be undone and will remove all associated files.`);

    if (!confirmed) return;

    setDeletingAppId(appId);
    try {
      const response = await fetch(`/api/apps/${appId}/delete`, {
        method: 'DELETE'
      });

      if (response.ok) {
        // Remove app from local state
        setApps(prevApps => prevApps.filter(app => app.id !== appId));
        toast.success(`"${appName}" has been deleted successfully`);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to delete app');
      }
    } catch (error) {
      console.error('Error deleting app:', error);
      toast.error('Failed to delete app');
    } finally {
      setDeletingAppId(null);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format downloads
  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K`;
    }
    return downloads.toString();
  };

  if (!session?.user?.admin) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingDots />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header type="admin" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            App Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Review and manage submitted applications
          </p>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm mb-8"
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search Bar */}
            <div className="relative flex-1 lg:mr-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search apps..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Status Filter */}
            <div className="flex items-center space-x-4">
              <Filter className="w-5 h-5 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="ALL">All Status</option>
                <option value="PENDING">Pending</option>
                <option value="APPROVED">Approved</option>
                <option value="REJECTED">Rejected</option>
                <option value="SUSPENDED">Suspended</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Apps List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm animate-pulse">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2 w-3/4"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : apps.length === 0 ? (
            <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No apps found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {apps.map((app) => {
                const StatusIcon = statusIcons[app.status];
                return (
                  <div
                    key={app.id}
                    className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        {/* App Icon */}
                        {app.iconUrl ? (
                          <Image
                            src={app.iconUrl.startsWith('http') ? app.iconUrl : `https://cdn.avehubs.com/f/${app.iconUrl.split('/').pop()}`}
                            alt={app.name}
                            width={64}
                            height={64}
                            className="rounded-lg"
                          />
                        ) : (
                          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                            <Package className="w-8 h-8 text-gray-500" />
                          </div>
                        )}

                        {/* App Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {app.name}
                            </h3>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[app.status]}`}>
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {app.status}
                            </span>
                          </div>

                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            by {app.developer.name} • v{app.version} • {app.category}
                          </p>

                          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                            {app.shortDescription || app.description}
                          </p>

                          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                            <span className="flex items-center">
                              <Download className="w-4 h-4 mr-1" />
                              {formatDownloads(app.downloads)}
                            </span>
                            <span className="flex items-center">
                              <MessageSquare className="w-4 h-4 mr-1" />
                              {app._count.comments}
                            </span>
                            <span className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {formatDate(app.createdAt)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => router.push(`/apps/${app.id}`)}
                          className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                          title="View App"
                        >
                          <Eye className="w-5 h-5" />
                        </button>

                        {app.status === "PENDING" && (
                          <>
                            <button
                              onClick={() => updateAppStatus(app.id, "APPROVED")}
                              disabled={processingId === app.id}
                              className="p-2 text-green-600 hover:text-green-700 transition-colors disabled:opacity-50"
                              title="Approve App"
                            >
                              <Check className="w-5 h-5" />
                            </button>
                            <button
                              onClick={() => updateAppStatus(app.id, "REJECTED")}
                              disabled={processingId === app.id}
                              className="p-2 text-red-600 hover:text-red-700 transition-colors disabled:opacity-50"
                              title="Reject App"
                            >
                              <X className="w-5 h-5" />
                            </button>
                          </>
                        )}

                        {app.status === "APPROVED" && (
                          <button
                            onClick={() => updateAppStatus(app.id, "SUSPENDED")}
                            disabled={processingId === app.id}
                            className="p-2 text-orange-600 hover:text-orange-700 transition-colors disabled:opacity-50"
                            title="Suspend App"
                          >
                            <Pause className="w-5 h-5" />
                          </button>
                        )}

                        {app.status === "SUSPENDED" && (
                          <button
                            onClick={() => updateAppStatus(app.id, "APPROVED")}
                            disabled={processingId === app.id}
                            className="p-2 text-green-600 hover:text-green-700 transition-colors disabled:opacity-50"
                            title="Reactivate App"
                          >
                            <Check className="w-5 h-5" />
                          </button>
                        )}

                        {/* Delete Button - Available for all statuses */}
                        <button
                          onClick={() => handleDeleteApp(app.id, app.name)}
                          disabled={deletingAppId === app.id || processingId === app.id}
                          className="p-2 text-red-600 hover:text-red-700 transition-colors disabled:opacity-50"
                          title="Delete App"
                        >
                          {deletingAppId === app.id ? (
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"></div>
                          ) : (
                            <Trash2 className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
