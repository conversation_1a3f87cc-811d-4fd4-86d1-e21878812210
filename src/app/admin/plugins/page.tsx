'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Edit,
  Trash2,
  Download,
  Plus,
  Search,
  Calendar,
  MessageSquare,
  Package,
  Eye
} from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useSession } from 'next-auth/react';
import { Header } from '@/components/Header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Plugin {
  id: string;
  name: string;
  description: string;
  publisherId: string;
  publisherName: string;
  version: string;
  releaseType: string;
  releaseDate: string;
  updateDate: string;
  fileLocation: string;
  downloads: number;
  _count: {
    comments: number;
  };
}

export default function AdminPluginsPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Dialog states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [currentPlugin, setCurrentPlugin] = useState<Plugin | null>(null);
  
  // Form states
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [version, setVersion] = useState('');
  const [releaseType, setReleaseType] = useState('release');
  const [publisherId, setPublisherId] = useState('');
  const [publisherName, setPublisherName] = useState('');
  const [pluginFile, setPluginFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize publisher info from session when it's available
  useEffect(() => {
    if (session?.user) {
      setPublisherId(session.user.id);
      setPublisherName(session.user.name || 'Unknown User');
    }
  }, [session]);

  // Check admin status
  useEffect(() => {
    if (status === 'authenticated' && !session?.user.admin) {
      toast.error("You don't have permission to access this page");
      router.push('/');
    }
  }, [status, session, router]);

  // Fetch plugins
  const fetchPlugins = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/plugins');
      if (response.ok) {
        const data = await response.json();
        setPlugins(data);
      } else {
        toast.error("Failed to fetch plugins");
      }
    } catch (error) {
      console.error('Error fetching plugins:', error);
      toast.error("Failed to fetch plugins");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (status === 'authenticated' && session?.user.admin) {
      fetchPlugins();
    }
  }, [status, session]);

  // Filter plugins by search query
  const filteredPlugins = plugins.filter(plugin => 
    plugin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    plugin.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    plugin.publisherName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Handle create plugin
  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name || !description || !version || !pluginFile) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    // Make sure publisherId and publisherName are set
    if (!publisherId || !publisherName) {
      // Try to get them from session if not already set
      if (session?.user) {
        setPublisherId(session.user.id);
        setPublisherName(session.user.name || 'Unknown User');
      } else {
        toast.error("Could not determine publisher information. Please try again.");
        return;
      }
    }
    
    try {
      setIsSubmitting(true);
      
      const formData = new FormData();
      formData.append('name', name);
      formData.append('description', description);
      formData.append('version', version);
      formData.append('releaseType', releaseType);
      formData.append('publisherId', publisherId);
      formData.append('publisherName', publisherName);
      formData.append('pluginFile', pluginFile);
      
      // Log form data for debugging
      console.log("Submitting plugin with:", {
        name,
        description,
        version,
        releaseType,
        publisherId,
        publisherName,
        pluginFile: pluginFile?.name
      });
      
      const response = await fetch('/api/admin/plugins', {
        method: 'POST',
        body: formData,
      });
      
      if (response.ok) {
        toast.success("Plugin created successfully");
        resetForm();
        setShowCreateDialog(false);
        fetchPlugins();
      } else {
        const error = await response.json();
        console.error("Server response:", error);
        throw new Error(error.error || 'Failed to create plugin');
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to create plugin");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit plugin
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentPlugin || !name || !description || !version) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const formData = new FormData();
      formData.append('name', name);
      formData.append('description', description);
      formData.append('version', version);
      formData.append('releaseType', releaseType);
      formData.append('publisherId', publisherId);
      formData.append('publisherName', publisherName);
      
      if (pluginFile) {
        formData.append('pluginFile', pluginFile);
      }
      
      const response = await fetch(`/api/admin/plugins/${currentPlugin.id}`, {
        method: 'PUT',
        body: formData,
      });
      
      if (response.ok) {
        toast.success("Plugin updated successfully");
        resetForm();
        setShowEditDialog(false);
        fetchPlugins();
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update plugin');
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to update plugin");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete plugin
  const handleDeletePlugin = async () => {
    if (!currentPlugin) return;
    
    try {
      const response = await fetch(`/api/admin/plugins/${currentPlugin.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        toast.success("Plugin deleted successfully");
        setShowDeleteDialog(false);
        fetchPlugins();
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete plugin');
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete plugin");
    }
  };

  // Open edit dialog
  const openEditDialog = (plugin: Plugin) => {
    setCurrentPlugin(plugin);
    setName(plugin.name);
    setDescription(plugin.description);
    setVersion(plugin.version);
    setReleaseType(plugin.releaseType || 'release');
    setPublisherId(plugin.publisherId);
    setPublisherName(plugin.publisherName);
    setPluginFile(null);
    setShowEditDialog(true);
  };

  // Open delete dialog
  const openDeleteDialog = (plugin: Plugin) => {
    setCurrentPlugin(plugin);
    setShowDeleteDialog(true);
  };

  // Reset form
  const resetForm = () => {
    setName('');
    setDescription('');
    setVersion('');
    setReleaseType('release');
    setPluginFile(null);
    // We intentionally don't reset publisherId and publisherName here
    // since they should always be set from the session
    setCurrentPlugin(null);
  };

  // Open create dialog
  const openCreateDialog = () => {
    resetForm();
    // Auto-populate publisher ID and name from the logged-in user
    if (session?.user) {
      console.log("Setting publisher info from session:", {
        id: session.user.id,
        name: session.user.name
      });
      setPublisherId(session.user.id);
      setPublisherName(session.user.name || 'Unknown User');
    } else {
      console.warn("No user session found for auto-populating publisher info");
    }
    setShowCreateDialog(true);
  };

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setPluginFile(e.target.files[0]);
    }
  };

  // Loading state
  if (status === 'loading') {
    return (
      <>
        <Header type="admin" />
        <div className="container mx-auto py-12 flex justify-center items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </>
    );
  }
  
  // Not admin
  if (status === 'authenticated' && !session?.user.admin) {
    return (
      <>
        <Header type="admin" />
        <div className="container mx-auto py-12 text-center">
          <h1 className="text-3xl font-bold text-red-500 mb-4">Access Denied</h1>
          <p className="mb-6">You don't have permission to access this page</p>
          <Button onClick={() => router.push('/')}>
            Go Home
          </Button>
        </div>
      </>
    );
  }

  return (
    <>
      <Header type="admin" />
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Plugins Manager</h1>
          <Button onClick={openCreateDialog} className="flex items-center gap-2">
            <Plus className="h-4 w-4" /> Add Plugin
          </Button>
        </div>

        {/* Search bar */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search plugins..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Plugin Management</CardTitle>
            <CardDescription>
              Create, edit, and manage plugins for your users
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : filteredPlugins.length === 0 ? (
              <div className="text-center py-16">
                <Package className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium">No plugins found</h3>
                <p className="text-muted-foreground mt-2">
                  {searchQuery ? "Try adjusting your search" : "Add your first plugin to get started"}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Publisher</TableHead>
                      <TableHead>Version</TableHead>
                      <TableHead>Released</TableHead>
                      <TableHead>Updated</TableHead>
                      <TableHead>Downloads</TableHead>
                      <TableHead>Comments</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPlugins.map((plugin) => (
                      <TableRow key={plugin.id}>
                        <TableCell className="font-medium">{plugin.name}</TableCell>
                        <TableCell>{plugin.publisherName}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{plugin.version}</Badge>
                        </TableCell>
                        <TableCell>{formatDate(plugin.releaseDate)}</TableCell>
                        <TableCell>{formatDate(plugin.updateDate)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Download className="h-3 w-3" />
                            {plugin.downloads}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3" />
                            {plugin._count.comments}
                          </div>
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => router.push(`/plugins/${plugin.id}`)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(plugin)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openDeleteDialog(plugin)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Create Plugin Dialog */}
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Plugin</DialogTitle>
              <DialogDescription>
                Fill in the details to create a new plugin
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateSubmit}>
              <div className="grid gap-4 py-4">
                <div>
                  <Label htmlFor="name">Plugin Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter plugin name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter plugin description"
                    className="min-h-[100px]"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={version}
                    onChange={(e) => setVersion(e.target.value)}
                    placeholder="e.g. 1.0.0"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="releaseType">Release Type</Label>
                  <Select 
                    value={releaseType} 
                    onValueChange={setReleaseType}
                  >
                    <SelectTrigger id="releaseType" className="w-full">
                      <SelectValue placeholder="Select release type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="release">Release</SelectItem>
                      <SelectItem value="snapshot">Snapshot</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    'Release' for stable versions, 'Snapshot' for development builds
                  </p>
                </div>
                <div>
                  <Label htmlFor="publisherId">Publisher ID</Label>
                  <Input
                    id="publisherId"
                    value={publisherId}
                    readOnly
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Auto-populated from your account
                  </p>
                </div>
                <div>
                  <Label htmlFor="publisherName">Publisher Name</Label>
                  <Input
                    id="publisherName"
                    value={publisherName}
                    readOnly
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Auto-populated from your account
                  </p>
                </div>
                <div>
                  <Label htmlFor="pluginFile">Plugin File</Label>
                  <Input
                    id="pluginFile"
                    type="file"
                    onChange={handleFileChange}
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowCreateDialog(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Plugin"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Plugin Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Plugin</DialogTitle>
              <DialogDescription>
                Update the plugin details
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleEditSubmit}>
              <div className="grid gap-4 py-4">
                <div>
                  <Label htmlFor="edit-name">Plugin Name</Label>
                  <Input
                    id="edit-name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter plugin name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter plugin description"
                    className="min-h-[100px]"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="edit-version">Version</Label>
                  <Input
                    id="edit-version"
                    value={version}
                    onChange={(e) => setVersion(e.target.value)}
                    placeholder="e.g. 1.0.0"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="edit-releaseType">Release Type</Label>
                  <Select 
                    value={releaseType} 
                    onValueChange={setReleaseType}
                  >
                    <SelectTrigger id="edit-releaseType" className="w-full">
                      <SelectValue placeholder="Select release type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="release">Release</SelectItem>
                      <SelectItem value="snapshot">Snapshot</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    'Release' for stable versions, 'Snapshot' for development builds
                  </p>
                </div>
                <div>
                  <Label htmlFor="edit-publisherId">Publisher ID</Label>
                  <Input
                    id="edit-publisherId"
                    value={publisherId}
                    readOnly
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Publisher ID cannot be changed
                  </p>
                </div>
                <div>
                  <Label htmlFor="edit-publisherName">Publisher Name</Label>
                  <Input
                    id="edit-publisherName"
                    value={publisherName}
                    readOnly
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Publisher name cannot be changed
                  </p>
                </div>
                <div>
                  <Label htmlFor="edit-pluginFile">Plugin File (Optional)</Label>
                  <Input
                    id="edit-pluginFile"
                    type="file"
                    onChange={handleFileChange}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Leave empty to keep the current file
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowEditDialog(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Updating..." : "Update Plugin"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete the plugin "{currentPlugin?.name}". 
                This action cannot be undone and all associated data including comments will be lost.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeletePlugin} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
} 