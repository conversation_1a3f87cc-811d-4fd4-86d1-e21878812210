'use client';
import { useSession } from 'next-auth/react';
import { Header } from '@/components/Header';
import { useRouter } from 'next/navigation';
import { useFetchLicenseRequests } from '@/hooks/license/useFetchLicenseRequests';
import { useUpdateLicenseStatus } from '@/hooks/license/useUpdateLicenseStatus';
import LoadingDots from '@/components/animations/Loading';

export default function LicenseAdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { requests, loading } = useFetchLicenseRequests();
  const { updateStatus, processingId } = useUpdateLicenseStatus();

  if (status === 'loading' || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingDots />
      </div>
    );
  }

  if (!session?.user?.admin) {
    router.push('/');
    return null;
  }

  return (
    <div>
      <Header type="admin" />
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">License Requests</h2>
        <table className="min-w-full text-left">
          <thead>
            <tr>
              <th className="px-2">User</th>
              <th className="px-2">Spigot</th>
              <th className="px-2">Screenshot</th>
              <th className="px-2">Status</th>
              <th className="px-2">Actions</th>
            </tr>
          </thead>
          <tbody>
            {requests.map(req => (
              <tr key={req.id} className="border-t">
                <td className="px-2">{req.userId}</td>
                <td className="px-2">{req.spigotUsername}</td>
                <td className="px-2"><a href={req.screenshotUrl} target="_blank" className="text-blue-500">View</a></td>
                <td className="px-2">{req.status}</td>
                <td className="px-2 space-x-2">
                  <button disabled={processingId===req.id} onClick={()=>updateStatus(req.id,'approved')} className="bg-green-600 px-2 py-1 rounded text-white">Approve</button>
                  <button disabled={processingId===req.id} onClick={()=>updateStatus(req.id,'rejected')} className="bg-red-600 px-2 py-1 rounded text-white">Reject</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
