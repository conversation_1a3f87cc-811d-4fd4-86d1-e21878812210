"use client";
import { useToggleAdmin } from "@/hooks/admin/useToggleAdmin";
import useAdminStats from "@/hooks/admin/useAdminStats";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { Header } from "@/components/Header";
import { useState, useEffect } from "react";
import LoadingDots from "@/components/animations/Loading";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Users,
  ClipboardList,
  Briefcase,
  RefreshCw,
  BarChart3,
  Server,
  ShieldAlert,
  UserCog,
  Clock,
  CalendarDays,
  ArrowUpRight,
  Package
} from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";

const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 }
};

const AdminHome = () => {
  const { stats, loading: statsLoading, refetch: refetchStats } = useAdminStats();
  const { data: session } = useSession();
  const [currentDateTime, setCurrentDateTime] = useState<string>("");

  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();
      setCurrentDateTime(
        now.toLocaleDateString() + ' ' +
        now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      );
    };

    // Initialize with current time and set interval to 60 seconds
    updateDateTime();
    const intervalId = setInterval(updateDateTime, 60000);

    return () => clearInterval(intervalId);
  }, []);

  if (statsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <LoadingDots />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
      case "accepted":
        return <Badge className="bg-green-500">Approved</Badge>;
      case "rejected":
        return <Badge className="bg-red-500">Rejected</Badge>;
      default:
        return <Badge className="bg-yellow-500">Pending</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <Header type="admin" />
      <div className="text-white flex flex-col items-center px-4 sm:px-6 py-8">
        <div className="w-full max-w-7xl">
          <motion.div
            className="mb-12"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            transition={{ duration: 0.3 }}
          >
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-center mt-8 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 text-transparent bg-clip-text">
              Welcome back, {session?.user?.name} 🚀
            </h1>
            <p className="text-gray-400 text-center mt-2">Whether you're managing users, tracking activity, or fine-tuning settings, everything you need is right at your fingertips.</p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-4 mb-8"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            transition={{ duration: 0.3 }}
          >

            <Card className="bg-gray-800 border-none shadow-lg overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Users className="mr-2 h-5 w-5 text-blue-400" />
                  Users
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold">{stats?.counts.users || 0}</div>
                <p className="text-sm text-gray-400 mt-1">
                  Including {stats?.counts.admins || 0} administrators
                </p>
              </CardContent>
              <CardFooter className="pt-0">
                <Link href="/admin/users" className="inline-flex w-full">
                  <Button variant="outline" size="sm" className="text-xs w-full">
                    Manage Users
                    <ArrowUpRight className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            <Card className="bg-gray-800 border-none shadow-lg overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <ClipboardList className="mr-2 h-5 w-5 text-green-400" />
                  Staff Applications
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold">{stats?.counts.pendingStaffApplications || 0}</div>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className="bg-yellow-500">{stats?.counts.pendingStaffApplications || 0} Pending</Badge>
                  <Badge className="bg-green-500">{stats?.counts.approvedStaffApplications || 0} Approved</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Link href="/admin/staffmanage" className="inline-flex w-full">
                  <Button variant="outline" size="sm" className="text-xs w-full">
                    Manage Applications
                    <ArrowUpRight className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            <Card className="bg-gray-800 border-none shadow-lg overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Briefcase className="mr-2 h-5 w-5 text-purple-400" />
                  Partnership Requests
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold">{stats?.counts.pendingPartnershipApplications || 0}</div>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className="bg-yellow-500">{stats?.counts.pendingPartnershipApplications || 0} Pending</Badge>
                  <Badge className="bg-green-500">{stats?.counts.approvedPartnershipApplications || 0} Approved</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Link href="/admin/partnershipmanage" className="inline-flex w-full">
                  <Button variant="outline" size="sm" className="text-xs w-full">
                    Manage Partnerships
                    <ArrowUpRight className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            <Card className="bg-gray-800 border-none shadow-lg overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Package className="mr-2 h-5 w-5 text-orange-400" />
                  Applications
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-3xl font-bold">{stats?.counts.totalApps || 0}</div>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className="bg-yellow-500">{stats?.counts.pendingApps || 0} Pending</Badge>
                  <Badge className="bg-green-500">{stats?.counts.approvedApps || 0} Approved</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Link href="/admin/apps" className="inline-flex w-full">
                  <Button variant="outline" size="sm" className="text-xs w-full">
                    Manage Apps
                    <ArrowUpRight className="ml-1 h-3 w-3" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 lg:grid-cols-3 gap-6"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            transition={{ duration: 0.3 }}
          >
            {/* Date and Time Card */}
            <Card className="bg-gray-800 border-none shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Clock className="mr-2 h-5 w-5 text-yellow-400" />
                  Current Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-24 space-y-2">
                  <div className="text-3xl font-mono font-bold text-white">{currentDateTime.split(' ')[1]}</div>
                  <div className="flex items-center">
                    <CalendarDays className="h-4 w-4 mr-2 text-gray-400" />
                    <span className="text-gray-300">{currentDateTime.split(' ')[0]}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions Card */}
            <Card className="bg-gray-800 border-none shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <ShieldAlert className="mr-2 h-5 w-5 text-red-400" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <Link href="/admin/users">
                    <Button variant="outline" className="w-full" size="sm">
                      <UserCog className="mr-2 h-4 w-4" />
                      Users
                    </Button>
                  </Link>
                  <Link href="/admin/staffmanage">
                    <Button variant="outline" className="w-full" size="sm">
                      <Users className="mr-2 h-4 w-4" />
                      Staff
                    </Button>
                  </Link>
                  <Link href="/admin/partnershipmanage">
                    <Button variant="outline" className="w-full" size="sm">
                      <Briefcase className="mr-2 h-4 w-4" />
                      Partners
                    </Button>
                  </Link>
                  <Link href="/admin/apps">
                    <Button variant="outline" className="w-full" size="sm">
                      <Package className="mr-2 h-4 w-4" />
                      Apps
                    </Button>
                  </Link>
                  <Link href="/admin/announcementcreate">
                    <Button variant="outline" className="w-full" size="sm">
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Announce
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity Summary */}
            <Card className="bg-gray-800 border-none shadow-lg">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5 text-orange-400" />
                    Activity Summary
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={refetchStats}
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">New Users (Last 24h):</span>
                    <span className="font-semibold">{stats?.recentActivity.users.length || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Staff Applications:</span>
                    <span className="font-semibold">{stats?.recentActivity.staffApplications.length || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Partnership Requests:</span>
                    <span className="font-semibold">{stats?.recentActivity.partnershipApplications.length || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Users */}
            <Card className="bg-gray-800 border-none shadow-lg">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg flex items-center">
                    <Users className="mr-2 h-5 w-5 text-blue-400" />
                    Recent Users
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                  {stats?.recentActivity.users.length ? (
                    stats.recentActivity.users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center space-x-3 bg-gray-700 rounded-lg p-3"
                      >
                        <Image
                          src={user.image || "/default-avatar.png"}
                          alt={user.name || "User"}
                          width={40}
                          height={40}
                          className="rounded-full border-2 border-gray-600"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">{user.name}</p>
                          <p className="text-xs text-gray-400 truncate">{user.email}</p>
                        </div>
                        {user.admin && (
                          <Badge className="bg-blue-600">Admin</Badge>
                        )}
                      </div>
                    ))
                  ) : (
                    <p className="text-center text-gray-400 py-4">No recent users</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Applications */}
            <Card className="bg-gray-800 border-none shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <ClipboardList className="mr-2 h-5 w-5 text-green-400" />
                  Recent Applications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6 max-h-96 overflow-y-auto pr-2">
                  {/* Staff Applications */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2">Staff Applications</h4>
                    {stats?.recentActivity.staffApplications.length ? (
                      <div className="space-y-2">
                        {stats.recentActivity.staffApplications.map((app) => (
                          <div
                            key={app.id}
                            className="bg-gray-700 rounded-lg p-3"
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{app.fullName}</p>
                                <p className="text-xs text-gray-400">{app.position}</p>
                              </div>
                              {getStatusBadge(app.status)}
                            </div>
                            <div className="mt-2 text-xs text-gray-400">
                              Applied: {formatDate(app.appliedAt)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-gray-400 py-2">No recent staff applications</p>
                    )}
                  </div>

                  {/* Partnership Applications */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2">Partnership Applications</h4>
                    {stats?.recentActivity.partnershipApplications.length ? (
                      <div className="space-y-2">
                        {stats.recentActivity.partnershipApplications.map((app) => (
                          <div
                            key={app.id}
                            className="bg-gray-700 rounded-lg p-3"
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{app.username}</p>
                                <p className="text-xs text-gray-400">{app.companyName}</p>
                              </div>
                              {getStatusBadge(app.status)}
                            </div>
                            <div className="mt-2 text-xs text-gray-400">
                              Applied: {formatDate(app.appliedAt)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-gray-400 py-2">No recent partnership applications</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminHome;
