"use client";
import { useState } from "react";
import { useCreateAnnouncement } from "@/hooks/announcement/admin-only/useAnnouncementActions";
import { Loader2 } from "lucide-react";
import { Header } from "@/components/Header"; 

export default function CreateAnnouncementPage() {
  const { createAnnouncement, loading, error } = useCreateAnnouncement();
  const [formData, setFormData] = useState({ title: "", description: "", footer: "" });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const announcement = await createAnnouncement(formData);
    if (announcement) alert("✅ Announcement created successfully!");
  };

  return (
    <div>
    <Header type="admin" />
    <div className="flex justify-center items-center min-h-screen bg-gray-900 p-4">
      <div className="w-full max-w-lg p-6 bg-gray-800 text-white shadow-2xl rounded-2xl transition-transform duration-300 hover:scale-105">
        <h1 className="text-3xl font-extrabold text-center mb-6">📢 Create Announcement</h1>

        {error && <p className="text-red-500 text-center bg-red-800 p-2 rounded mb-4">{error}</p>}

        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <div className="relative">
            <input
              type="text"
              name="title"
              placeholder="Enter Title"
              value={formData.title}
              onChange={handleChange}
              className="w-full p-3 bg-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 outline-none placeholder-gray-400"
              required
            />
          </div>

          <div className="relative">
            <textarea
              name="description"
              placeholder="Enter Description"
              value={formData.description}
              onChange={handleChange}
              className="w-full p-3 bg-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 outline-none placeholder-gray-400 resize-none"
              required
            />
          </div>

          <div className="relative">
            <input
              type="text"
              name="footer"
              placeholder="Enter Footer"
              value={formData.footer}
              onChange={handleChange}
              className="w-full p-3 bg-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 outline-none placeholder-gray-400"
              required
            />
          </div>

          <button
            type="submit"
            className="flex justify-center items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-lg text-lg font-semibold shadow-md hover:opacity-80 transition-all duration-300 disabled:opacity-50"
            disabled={loading}
          >
            {loading ? <Loader2 className="animate-spin h-5 w-5" /> : "🚀 Create Announcement"}
          </button>
        </form>
      </div>
    </div>
    </div>
  );
}