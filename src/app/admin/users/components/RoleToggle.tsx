"use client";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Switch } from "@/components/ui/switch";
import useToggleAdmin from "@/hooks/admin/useToggleAdmin";

interface User {
  id: string;
  email: string | null;
  admin: boolean;
}

interface ManageRolesProps {
  user: User;
  isUserLoading: (userId: string) => boolean;
}

export default function ManageRolesButton({
  user,
  isUserLoading,
}: ManageRolesProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentAdmin, setCurrentAdmin] = useState(user.admin);
  const { toggleAdmin, isUserLoading: adminToggleLoading } = useToggleAdmin();

  const handleAdminToggle = async (checked: boolean) => {
    const newAdmin = await toggleAdmin(user.id, currentAdmin);
    setCurrentAdmin(newAdmin);
  };

  const isAdminBusy = adminToggleLoading(user.id);
  const isDisabled = isUserLoading(user.id) || isAdminBusy;

  return (
    <div className="relative inline-block text-left">
      <motion.button
        type="button"
        onClick={() => setIsOpen((prev) => !prev)}
        className="px-4 py-2 bg-blue-800 text-white rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        Manage Roles
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            key="dropdown"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="absolute right-0 mt-2 w-56 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10 overflow-hidden text-white"
          >
            <motion.div
              className="px-4 py-2 bg-gray-900 border-b border-gray-700"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.15 }}
            >
              <span className="text-sm font-semibold">Role Manager</span>
            </motion.div>
            <motion.div
              className="flex items-center justify-between px-4 py-2 hover:bg-gray-700"
              whileHover={{ backgroundColor: "#374151" }}
            >
              <span className="text-sm font-medium">Admin</span>
              <Switch
                checked={currentAdmin}
                onCheckedChange={handleAdminToggle}
                disabled={isDisabled}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
