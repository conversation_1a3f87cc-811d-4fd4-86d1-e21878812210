"use client";

import { useState, useEffect, useMemo } from "react";
import { useUsers } from "@/hooks/useUsers";
import { useToggleAdmin } from "@/hooks/admin/useToggleAdmin";
import { useUpdateUser, useDeleteUser, User } from "@/hooks/admin/useUserManagement";
import { useSendEmail } from "@/hooks/admin/useUserEmail";
import { Header } from "@/components/Header";
import LoadingDots from "@/components/animations/Loading";
import Image from "next/image";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import ManageRolesButton from "./components/RoleToggle";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Edit,
  Trash2,
  UserCog,
  Loader2,
  Mail,
  Users as UsersIcon,
  User as UserIcon,
  Send
} from "lucide-react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";

export default function UsersManagementPage() {
  const { users, loading } = useUsers();
  const { toggleAdmin, isUserLoading } = useToggleAdmin();
  const { updateUser, updating, updatingId } = useUpdateUser();
  const { deleteUser, deleting, deletingId } = useDeleteUser();
  const { sendEmail, sendBulkEmail, sending } = useSendEmail();
  const { data: session } = useSession();
  
  const [userList, setUserList] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [editName, setEditName] = useState("");
  const [editAdmin, setEditAdmin] = useState(false);
  
  // Email dialog state
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [emailTitle, setEmailTitle] = useState("");
  const [emailContent, setEmailContent] = useState("");
  const [emailFooter, setEmailFooter] = useState("");
  const [recipientType, setRecipientType] = useState<"all" | "select">("all");
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [userSearchTerm, setUserSearchTerm] = useState("");

  // Initialize user list from fetched users
  useEffect(() => {
    if (users) {
      setUserList(users as User[]);
    }
  }, [users]);

  // Handle search filtering
  const filteredUsers = userList.filter((user) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      (user.name && user.name.toLowerCase().includes(searchLower)) ||
      (user.email && user.email.toLowerCase().includes(searchLower))
    );
  });

  // Filter users for the recipient selection dropdown
  const filteredRecipients = useMemo(() => {
    if (!userSearchTerm) return userList;
    
    const searchLower = userSearchTerm.toLowerCase();
    return userList.filter(user => 
      (user.name && user.name.toLowerCase().includes(searchLower)) ||
      (user.email && user.email.toLowerCase().includes(searchLower))
    );
  }, [userList, userSearchTerm]);

  // Open edit dialog
  const handleEdit = (user: User) => {
    setEditingUser(user);
    setEditName(user.name || "");
    setEditAdmin(user.admin);
    setIsEditDialogOpen(true);
  };

  // Handle save in edit dialog
  const handleSaveEdit = async () => {
    if (!editingUser) return;

    // Only update if changes were made
    if (editName !== editingUser.name || editAdmin !== editingUser.admin) {
      const updatedUser = await updateUser(editingUser.id, {
        name: editName,
        admin: editAdmin,
      });

      if (updatedUser) {
        // Update the user in the list
        setUserList((prevUsers) =>
          prevUsers.map((user) =>
            user.id === updatedUser.id ? updatedUser : user
          )
        );
      }
    }

    setIsEditDialogOpen(false);
  };

  // Handle toggle admin - optimized version
  const handleToggleAdmin = async (user: User) => {
    // Call toggleAdmin and get the new admin status
    const newAdminStatus = await toggleAdmin(user.id, user.admin);
    
    // Update local state to reflect the change
    setUserList((prevUsers) => prevUsers.map(u => 
      u.id === user.id ? { ...u, admin: newAdminStatus } : u
    ));
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (user: User) => {
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  // Handle user deletion
  const handleConfirmDelete = async () => {
    if (!userToDelete) return;

    // Check if user is deleting themselves
    if (userToDelete.email === session?.user?.email) {
      toast.error("You cannot delete your own account");
      setIsDeleteDialogOpen(false);
      return;
    }

    const success = await deleteUser(userToDelete.id);

    if (success) {
      // Remove the user from the list
      setUserList((prevUsers) =>
        prevUsers.filter((user) => user.id !== userToDelete.id)
      );
    }

    setIsDeleteDialogOpen(false);
  };

  // Open email dialog
  const handleEmailClick = () => {
    setEmailTitle("");
    setEmailContent("");
    setEmailFooter("");
    setRecipientType("all");
    setSelectedUsers([]);
    setUserSearchTerm("");
    setIsEmailDialogOpen(true);
  };

  // Add a user to selected recipients
  const handleAddRecipient = (userId: string) => {
    const user = userList.find(u => u.id === userId);
    if (user && !selectedUsers.some(u => u.id === userId)) {
      setSelectedUsers(prev => [...prev, user]);
      setUserSearchTerm("");
    }
  };

  // Remove a user from selected recipients
  const handleRemoveRecipient = (userId: string) => {
    setSelectedUsers(prev => prev.filter(u => u.id !== userId));
  };

  // Send the email
  const handleSendEmail = async () => {
    if (!emailTitle.trim()) {
      toast.error("Email title is required");
      return;
    }

    if (!emailContent.trim()) {
      toast.error("Email content is required");
      return;
    }

    let success;

    if (recipientType === "all") {
      success = await sendBulkEmail("all", emailTitle, emailContent, emailFooter);
    } else {
      if (selectedUsers.length === 0) {
        toast.error("Please select at least one recipient");
        return;
      }
      success = await sendBulkEmail(selectedUsers, emailTitle, emailContent, emailFooter);
    }

    if (success?.success) {
      toast.success(`Email sent to ${recipientType === "all" ? "all users" : `${selectedUsers.length} users`}`);
      setIsEmailDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <LoadingDots />
      </div>
    );
  }

  return (
    <div>
      <Header type="admin" />
      <div className="container mx-auto py-8 px-4 min-h-screen bg-gray-900 text-white">
        <Card className="bg-gray-800 border-none shadow-lg">
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div>
                <CardTitle className="text-xl md:text-2xl flex items-center">
                  <UserCog className="mr-2 h-6 w-6 text-blue-400" />
                  User Management
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Manage all users, admin permissions, and accounts
                </CardDescription>
              </div>
              
              <div className="flex gap-2 flex-col sm:flex-row">
                <div className="relative w-full sm:w-64">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search users..."
                    className="pl-8 bg-gray-700 border-gray-600 text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button 
                  variant="default" 
                  className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700"
                  onClick={handleEmailClick}
                >
                  <Mail className="h-4 w-4" />
                  Send Email
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border border-gray-700">
              <Table>
                <TableHeader className="bg-gray-700">
                  <TableRow>
                    <TableHead className="w-[50px]">Avatar</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10 text-gray-400">
                        {searchTerm ? "No users found matching your search" : "No users found"}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id} className="border-t border-gray-700">
                        <TableCell>
                          <div className="relative w-10 h-10 overflow-hidden rounded-full">
                            <Image 
                              src={user.image || "/default-avatar.png"} 
                              alt={user.name || "User"} 
                              width={40} 
                              height={40} 
                              className="object-cover"
                            />
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-2">
                              <div className="flex items-center gap-2">
                                {isUserLoading(user.id) ? (
                                  <div className="w-4 h-4 mr-1">
                                    <div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
                                  </div>
                                ) : null}
                                <ManageRolesButton user={user} isUserLoading={isUserLoading} />
                              </div>
                             
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleEdit(user)}
                              disabled={updating && updatingId === user.id}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="destructive"
                              size="icon"
                              onClick={() => handleDeleteClick(user)}
                              disabled={
                                (deleting && deletingId === user.id) || 
                                user.email === session?.user?.email
                              }
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription className="text-gray-400">
              Make changes to user information
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium text-gray-300">
                Name
              </label>
              <Input
                id="name"
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300">Admin Status</label>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={editAdmin}
                  onCheckedChange={setEditAdmin}
                  disabled={editingUser?.email === session?.user?.email}
                />
                <span className={editAdmin ? "text-blue-400" : "text-gray-400"}>
                  {editAdmin ? "Admin" : "User"}
                </span>
                {editingUser?.email === session?.user?.email && (
                  <Badge className="ml-2 bg-blue-600 text-xs">You</Badge>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setIsEditDialogOpen(false)}
              className="text-gray-300"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSaveEdit}
              disabled={updating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {updating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving...
                </>
              ) : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="bg-gray-800 text-white border-gray-700">
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-400">
              Are you sure you want to delete this user? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-700 text-white hover:bg-gray-600">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={handleConfirmDelete}
              disabled={deleting}
            >
              {deleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Deleting...
                </>
              ) : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Email Dialog */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700 sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Mail className="mr-2 h-5 w-5 text-blue-400" />
              Send Email
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              Compose and send an email to users
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            {/* Select recipients */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300">Recipients</label>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={recipientType === "all"}
                    onCheckedChange={(checked) => setRecipientType(checked ? "all" : "select")}
                  />
                  <span className={recipientType === "all" ? "text-blue-400" : "text-gray-400"}>
                    All Users ({userList.length})
                  </span>
                </div>
              </div>
              
              {recipientType === "select" && (
                <div className="mt-4 space-y-2">
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Input
                        placeholder="Search users to add..."
                        className="bg-gray-700 border-gray-600 text-white"
                        value={userSearchTerm}
                        onChange={(e) => setUserSearchTerm(e.target.value)}
                      />
                    </div>
                    
                    <Select onValueChange={handleAddRecipient}>
                      <SelectTrigger className="w-[160px]">
                        <SelectValue placeholder="Add recipient" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Users</SelectLabel>
                          {filteredRecipients.length > 0 ? (
                            filteredRecipients
                              .filter(user => !selectedUsers.some(u => u.id === user.id))
                              .map(user => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.name || user.email}
                                </SelectItem>
                              ))
                          ) : (
                            <SelectItem value="none" disabled>
                              No users found
                            </SelectItem>
                          )}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Selected recipients */}
                  <div className="space-y-2 max-h-[140px] overflow-y-auto">
                    {selectedUsers.length === 0 ? (
                      <p className="text-sm text-gray-400 text-center py-2">
                        No recipients selected
                      </p>
                    ) : (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-300">
                            Selected ({selectedUsers.length})
                          </span>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-xs h-7 text-gray-400"
                            onClick={() => setSelectedUsers([])}
                          >
                            Clear all
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {selectedUsers.map(user => (
                            <div 
                              key={user.id}
                              className="flex items-center gap-1 rounded-full bg-gray-700 px-3 py-1 text-sm"
                            >
                              <span className="truncate max-w-[140px]">
                                {user.name || user.email}
                              </span>
                              <button
                                onClick={() => handleRemoveRecipient(user.id)}
                                className="text-gray-400 hover:text-gray-200"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Email Title */}
            <div className="space-y-2">
              <label htmlFor="email-title" className="text-sm font-medium text-gray-300">
                Email Title
              </label>
              <Input
                id="email-title"
                placeholder="Enter email title"
                value={emailTitle}
                onChange={(e) => setEmailTitle(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            {/* Email Content */}
            <div className="space-y-2">
              <label htmlFor="email-content" className="text-sm font-medium text-gray-300">
                Email Content
              </label>
              <Textarea
                id="email-content"
                placeholder="Write your email content here..."
                value={emailContent}
                onChange={(e) => setEmailContent(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white resize-none min-h-[120px]"
              />
            </div>
            
            {/* Email Footer */}
            <div className="space-y-2">
              <label htmlFor="email-footer" className="text-sm font-medium text-gray-300">
                Email Footer (Optional)
              </label>
              <Input
                id="email-footer"
                placeholder="Additional information for the footer"
                value={emailFooter}
                onChange={(e) => setEmailFooter(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="ghost"
              onClick={() => setIsEmailDialogOpen(false)}
              className="text-gray-300"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSendEmail}
              disabled={sending}
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-1"
            >
              {sending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  Send Email
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 