/*
 * Session Refresh API Route - Force refresh user session data
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import db from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: "No active session found"
      }, { status: 401 });
    }

    // Fetch fresh user data from database
    const freshUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        name: true,
        admin: true,
        isDeveloper: true,
        image: true
      }
    });

    if (!freshUser) {
      return NextResponse.json({
        success: false,
        error: "User not found in database"
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: "Session will be refreshed on next request",
      data: {
        currentSession: {
          id: session.user.id,
          email: session.user.email,
          isDeveloper: session.user.isDeveloper,
          admin: session.user.admin
        },
        freshData: {
          id: freshUser.id,
          email: freshUser.email,
          isDeveloper: freshUser.isDeveloper,
          admin: freshUser.admin
        },
        dataMatches: session.user.isDeveloper === freshUser.isDeveloper
      }
    });

  } catch (error) {
    console.error("Session refresh error:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to refresh session",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
