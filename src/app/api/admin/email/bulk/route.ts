import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/db";

// The existing email API only handles single emails, so we'll batch them here
// This could be optimized with a proper bulk email service in production

export async function POST(req: NextRequest) {
  try {
    // Get the session to verify the user is an admin
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "Unauthorized: You must be logged in." },
        { status: 401 }
      );
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user?.admin) {
      return NextResponse.json(
        { error: "Forbidden: Only admins can send bulk emails." },
        { status: 403 }
      );
    }

    // API Key validation
    const apiKey = req.headers.get("x-api-key");
    const expectedApiKey = process.env.NEXT_PUBLIC_API_KEY || "";
    
    if (apiKey !== expectedApiKey) {
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // Parse request body
    const { emails, allUsers, title, description, footer } = await req.json();

    // Basic validation
    if (!title?.trim()) {
      return NextResponse.json(
        { error: "Title is required." },
        { status: 400 }
      );
    }

    // Get email addresses
    let recipientEmails: string[] = [];
    
    if (allUsers) {
      // Get all users with valid emails
      const allUsersData = await prisma.user.findMany({
        select: { email: true },
        where: {
          email: { not: null }
        }
      });
      
      recipientEmails = allUsersData
        .map((user: { email: string | null }) => user.email as string)
        .filter((email: string) => email && email.includes('@'));
        
    } else if (Array.isArray(emails) && emails.length > 0) {
      // Filter out any invalid emails
      recipientEmails = emails.filter(email => 
        typeof email === 'string' && 
        email.trim() !== '' && 
        email.includes('@')
      );
    }

    if (recipientEmails.length === 0) {
      return NextResponse.json(
        { error: "No valid email recipients found." },
        { status: 400 }
      );
    }

    // Send emails in sequence
    // Note: In production, you might want to use a proper email service with batch capabilities
    const sentCount = await sendEmails(recipientEmails, title, description, footer);

    return NextResponse.json(
      { 
        success: true,
        sentCount,
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Error sending bulk emails:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    
    return NextResponse.json(
      { 
        error: "Failed to send emails",
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

// Helper function to send multiple emails
async function sendEmails(
  emails: string[], 
  title: string, 
  description: string, 
  footer?: string
): Promise<number> {
  let successCount = 0;
  
  // We'll use the existing email API
  const apiKey = process.env.NEXT_PUBLIC_API_KEY || "";
  
  for (const email of emails) {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/mail/sender`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey
        },
        body: JSON.stringify({
          email,
          title,
          description,
          footer
        }),
      });
      
      if (response.ok) {
        successCount++;
      } else {
        console.error(`Failed to send email to ${email}:`, await response.text());
      }
    } catch (error) {
      console.error(`Error sending email to ${email}:`, error);
    }
    
    // Add a small delay to avoid overwhelming the email server
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return successCount;
} 