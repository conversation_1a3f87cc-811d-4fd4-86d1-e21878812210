import { NextResponse } from "next/server";
import prisma from "@/lib/db";
import { auth } from "@/auth";

export async function GET() {
  try {
    // Verify authorization
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user is an admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { admin: true },
    });

    if (!currentUser?.admin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get statistics
    const [
      totalUsers,
      totalAdmins,
      pendingStaffApplications,
      approvedStaffApplications,
      pendingPartnershipApplications,
      approvedPartnershipApplications,
      totalApps,
      pendingApps,
      approvedApps,
      rejectedApps,
      suspendedApps,
      recentUsers,
      recentStaffApplications,
      recentPartnershipApplications
    ] = await Promise.all([
      // Count total users
      prisma.user.count(),

      // Count admin users
      prisma.user.count({
        where: { admin: true }
      }),

      // Count pending staff applications
      prisma.staffApplication.count({
        where: { status: "pending" }
      }),

      // Count approved staff applications
      prisma.staffApplication.count({
        where: { status: "accepted" }
      }),

      // Count pending partnership applications
      prisma.partnershipApplication.count({
        where: { status: "pending" }
      }),

      // Count approved partnership applications
      prisma.partnershipApplication.count({
        where: { status: { in: ["accepted", "approved"] } }
      }),

      // Count total apps
      prisma.app.count(),

      // Count pending apps
      prisma.app.count({
        where: { status: "PENDING" }
      }),

      // Count approved apps
      prisma.app.count({
        where: { status: "APPROVED" }
      }),

      // Count rejected apps
      prisma.app.count({
        where: { status: "REJECTED" }
      }),

      // Count suspended apps
      prisma.app.count({
        where: { status: "SUSPENDED" }
      }),

      // Get recent users
      prisma.user.findMany({
        orderBy: { id: "desc" },
        take: 5,
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          admin: true
        }
      }),

      // Get recent staff applications
      prisma.staffApplication.findMany({
        orderBy: { appliedAt: "desc" },
        take: 3,
        select: {
          id: true,
          fullName: true,
          position: true,
          status: true,
          appliedAt: true,
          userId: true
        }
      }),

      // Get recent partnership applications
      prisma.partnershipApplication.findMany({
        orderBy: { appliedAt: "desc" },
        take: 3,
        select: {
          id: true,
          username: true,
          companyName: true,
          status: true,
          appliedAt: true,
          userId: true
        }
      })
    ]);

    // Collect email addresses for recent applications
    const userIds = [
      ...recentStaffApplications.map(app => app.userId),
      ...recentPartnershipApplications.map(app => app.userId)
    ];

    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds }
      },
      select: {
        id: true,
        email: true
      }
    });

    // Create a mapping of user IDs to emails
    const emailMap = new Map();
    users.forEach(user => {
      emailMap.set(user.id, user.email);
    });

    // Add emails to the applications
    const staffApplicationsWithEmail = recentStaffApplications.map(app => ({
      ...app,
      email: emailMap.get(app.userId) || null
    }));

    const partnershipApplicationsWithEmail = recentPartnershipApplications.map(app => ({
      ...app,
      email: emailMap.get(app.userId) || null
    }));

    return NextResponse.json({
      counts: {
        users: totalUsers,
        admins: totalAdmins,
        pendingStaffApplications,
        approvedStaffApplications,
        pendingPartnershipApplications,
        approvedPartnershipApplications,
        totalApps,
        pendingApps,
        approvedApps,
        rejectedApps,
        suspendedApps
      },
      recentActivity: {
        users: recentUsers,
        staffApplications: staffApplicationsWithEmail,
        partnershipApplications: partnershipApplicationsWithEmail
      }
    });
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}