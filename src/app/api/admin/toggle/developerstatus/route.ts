import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

export async function POST(req: NextRequest) {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { isDeveloper: !user.isDeveloper },
    });

    return NextResponse.json({ success: true, isDeveloper: updatedUser.isDeveloper });
  } catch (error) {
    console.error("Error toggling developer status:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
