/*
 * Official Avehub Code, verified
 * Admin Toggle API Route - Handles toggling user admin status
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

// [1] POST - Toggle admin status for a user
export async function POST(req: NextRequest) {
  try {
    // [1.1] Extract userId from request body
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }

    // [1.2] Find the user in the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // [1.3] Update user's admin status (toggle it)
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { admin: !user.admin },
    });

    // [1.4] Return success response with updated admin status
    return NextResponse.json({ success: true, admin: updatedUser.admin });
  } catch (error) {
    // [1.5] Handle and log any errors
    console.error("Error toggling admin status:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
