const serverStartTime = Date.now();

export async function GET(req: Request) {
  const now = Date.now();
  const uptimeMs = now - serverStartTime;

  const seconds = Math.floor(uptimeMs / 1000) % 60;
  const minutes = Math.floor(uptimeMs / (1000 * 60)) % 60;
  const hours = Math.floor(uptimeMs / (1000 * 60 * 60)) % 24;
  const days = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));

  const uptimeStr = `${days}d ${hours}h ${minutes}m ${seconds}s`;

  return new Response(JSON.stringify({ uptime: uptimeStr }), {
    headers: { 'Content-Type': 'application/json' }
  });
}
