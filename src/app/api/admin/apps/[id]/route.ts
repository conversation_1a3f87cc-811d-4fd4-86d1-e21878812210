/*
 * Admin App Management API Route
 * Handles app status updates and admin operations
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/db";

// PATCH - Update app status (admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and admin status
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { id } = await params;

    // Parse request body
    const data = await request.json();
    const { status, reason } = data;

    // Validate status
    const validStatuses = ["PENDING", "APPROVED", "REJECTED", "SUSPENDED"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json({
        error: "Invalid status. Must be one of: " + validStatuses.join(", ")
      }, { status: 400 });
    }

    // Check if app exists
    const existingApp = await prisma.app.findUnique({
      where: { id },
      include: {
        developer: {
          select: { id: true, name: true, email: true }
        }
      }
    });

    if (!existingApp) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    // Update app status
    const updatedApp = await prisma.app.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date()
      },
      include: {
        developer: {
          select: { id: true, name: true, image: true }
        },
        _count: {
          select: { comments: true }
        }
      }
    });

    // Log the status change
    console.log(`📋 App status updated by admin:`, {
      appId: id,
      appName: existingApp.name,
      oldStatus: existingApp.status,
      newStatus: status,
      adminId: session.user.id,
      adminEmail: session.user.email,
      reason: reason || "No reason provided"
    });

    // TODO: Send email notification to developer about status change
    // This could be implemented later with a notification service

    return NextResponse.json(updatedApp);
  } catch (error) {
    console.error("Error updating app status:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// GET - Get app details (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and admin status
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { id } = await params;

    // Fetch app with all details
    const app = await prisma.app.findUnique({
      where: { id },
      include: {
        developer: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            isDeveloper: true
          }
        },
        comments: {
          include: {
            user: {
              select: { id: true, name: true, image: true }
            }
          },
          orderBy: { createdAt: "desc" }
        },
        versions: {
          orderBy: { createdAt: "desc" }
        },
        _count: {
          select: { comments: true }
        }
      }
    });

    if (!app) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    return NextResponse.json(app);
  } catch (error) {
    console.error("Error fetching app details:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// DELETE - Delete app (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and admin status
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { id } = await params;

    // Check if app exists
    const existingApp = await prisma.app.findUnique({
      where: { id },
      include: {
        developer: {
          select: { id: true, name: true, email: true }
        }
      }
    });

    if (!existingApp) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    // Delete app (this will cascade delete comments and versions)
    await prisma.app.delete({
      where: { id }
    });

    // Log the deletion
    console.log(`🗑️ App deleted by admin:`, {
      appId: id,
      appName: existingApp.name,
      developerId: existingApp.developerId,
      developerName: existingApp.developer.name,
      adminId: session.user.id,
      adminEmail: session.user.email
    });

    return NextResponse.json({ message: "App deleted successfully" });
  } catch (error) {
    console.error("Error deleting app:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
