import { NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { auth } from '@/auth';
import { getFilePreview } from '@/lib/appwrite';

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const requests = await prisma.licenseRequest.findMany({ orderBy: { requestedAt: 'desc' } });
    const formatted = requests.map(r => ({
      ...r,
      screenshotUrl: getFilePreview(r.screenshotUrl)
    }));
    return NextResponse.json(formatted);
  } catch (err) {
    console.error('License list error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
