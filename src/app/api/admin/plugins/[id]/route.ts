import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import db from "@/lib/db";
import { uploadPluginFile, deletePluginFile } from "@/lib/supabase";

// GET a single plugin by ID for admin
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { id: pluginId } = await params;

    const plugin = await db.plugin.findUnique({
      where: { id: pluginId },
      include: {
        _count: { select: { comments: true } },
        comments: { orderBy: { createdAt: "desc" } }
      }
    });

    if (!plugin) {
      return NextResponse.json({ error: "Plugin not found" }, { status: 404 });
    }

    return NextResponse.json(plugin);
  } catch (error) {
    console.error("Error fetching plugin:", error);
    return NextResponse.json({ error: "Failed to fetch plugin" }, { status: 500 });
  }
}

// UPDATE a plugin by ID for admin
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { id: pluginId } = await params;
    const formData = await req.formData();
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const version = formData.get("version") as string;
    const releaseType = formData.get("releaseType") as string || "release";
    const publisherId = formData.get("publisherId") as string;
    const publisherName = formData.get("publisherName") as string;
    const pluginFile = formData.get("pluginFile") as File | null;

    const existingPlugin = await db.plugin.findUnique({ where: { id: pluginId } });
    if (!existingPlugin) {
      return NextResponse.json({ error: "Plugin not found" }, { status: 404 });
    }

    const updateData: any = { 
      name, 
      description, 
      version, 
      releaseType: releaseType === "snapshot" ? "snapshot" : "release",
      publisherId, 
      publisherName 
    };

    if (pluginFile) {
      // Generate unique filename - include timestamp to avoid conflicts
      const filename = `${Date.now()}-${pluginFile.name.replace(/\s+/g, '-')}`;
      const fileBuffer = await pluginFile.arrayBuffer();
      const fileUrl = await uploadPluginFile(fileBuffer, filename);
      
      // If previous file exists, attempt to delete it from Supabase (ignore errors)
      if (existingPlugin.fileLocation && existingPlugin.fileLocation.includes('supabase')) {
        try {
          await deletePluginFile(existingPlugin.fileLocation);
        } catch (error) {
          console.warn('Failed to delete old plugin file, continuing anyway:', error);
        }
      }
      
      updateData.fileLocation = fileUrl;
    }

    const updatedPlugin = await db.plugin.update({ where: { id: pluginId }, data: updateData });
    return NextResponse.json(updatedPlugin);
  } catch (error) {
    console.error("Error updating plugin:", error);
    return NextResponse.json({ error: "Failed to update plugin" }, { status: 500 });
  }
}

// DELETE a plugin by ID for admin
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { id: pluginId } = await params;
    
    // Fetch plugin before deletion to get file path
    const plugin = await db.plugin.findUnique({ where: { id: pluginId } });
    
    if (!plugin) {
      return NextResponse.json({ error: "Plugin not found" }, { status: 404 });
    }
    
    // Delete from database
    await db.plugin.delete({ where: { id: pluginId } });
    
    // If file exists in Supabase, attempt to delete it (ignore errors)
    if (plugin.fileLocation && plugin.fileLocation.includes('supabase')) {
      try {
        await deletePluginFile(plugin.fileLocation);
      } catch (error) {
        console.warn('Failed to delete plugin file from storage, continuing anyway:', error);
      }
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting plugin:", error);
    return NextResponse.json({ error: "Failed to delete plugin" }, { status: 500 });
  }
}
