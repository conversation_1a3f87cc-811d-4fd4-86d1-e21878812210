import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import db from "@/lib/db";
import { uploadPluginFile, initSupabaseStorage } from "@/lib/supabase";

// GET all plugins for admin
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    // Check if user is admin
    if (!session?.user?.admin) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Fetch all plugins
    const plugins = await db.plugin.findMany({
      orderBy: { updateDate: "desc" },
      include: {
        _count: {
          select: { comments: true }
        }
      }
    });

    return NextResponse.json(plugins);
  } catch (error) {
    console.error("Error fetching plugins for admin:", error);
    return NextResponse.json(
      { error: "Failed to fetch plugins" },
      { status: 500 }
    );
  }
}

// POST to create new plugin by admin
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    // Check if user is admin
    if (!session?.user?.admin) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Initialize Supabase storage to ensure bucket exists
    await initSupabaseStorage();

    const formData = await req.formData();
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const version = formData.get("version") as string;
    const releaseType = formData.get("releaseType") as string || "release";
    const publisherId = formData.get("publisherId") as string;
    const publisherName = formData.get("publisherName") as string;
    const pluginFile = formData.get("pluginFile") as File;

    // Log received values for debugging
    console.log("Received plugin data:", {
      name,
      description,
      version,
      releaseType,
      publisherId,
      publisherName,
      pluginFile: pluginFile?.name || null
    });

    // Validate inputs
    if (!name || !description || !version || !publisherId || !publisherName || !pluginFile) {
      console.log("Missing fields:", {
        name: !name,
        description: !description,
        version: !version,
        publisherId: !publisherId,
        publisherName: !publisherName,
        pluginFile: !pluginFile
      });
      
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Generate unique filename - include timestamp to avoid conflicts
    const filename = `${Date.now()}-${pluginFile.name.replace(/\s+/g, '-')}`;
    
    // Upload file to Supabase storage
    const fileBuffer = await pluginFile.arrayBuffer();
    const fileUrl = await uploadPluginFile(fileBuffer, filename);

    // Create plugin record
    const plugin = await db.plugin.create({
      data: {
        name,
        description,
        version,
        releaseType: releaseType === "snapshot" ? "snapshot" : "release", // Ensure valid value
        publisherId,
        publisherName,
        fileLocation: fileUrl,
      }
    });

    return NextResponse.json(plugin);
  } catch (error) {
    console.error("Error creating plugin:", error);
    return NextResponse.json(
      { error: "Failed to create plugin" },
      { status: 500 }
    );
  }
} 