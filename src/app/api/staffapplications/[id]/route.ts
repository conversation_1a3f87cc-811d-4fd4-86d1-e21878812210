import { NextResponse } from "next/server";
import { type NextRequest } from "next/server";
import prisma from "@/lib/db";

const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

// Define a local interface for StaffApplication
interface StaffApp {
  id: string;
  userId: string;
  fullName: string;
  age: number;
  location: string;
  availability: string;
  position: string;
  experience: string;
  skills: string;
  motivation: string;
  portfolioUrl?: string | null;
  additionalInfo?: string | null;
  status: string;
  appliedAt: Date;
  statusUpdatedAt?: Date | null;
}

// Define a user interface for type safety
interface User {
  id: string;
  email: string | null;
}

// Use a single parameter approach for Next.js 15
export async function PATCH(request: NextRequest) {
  try {
    // Extract ID from URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // API Key validation
    const apiKey = request.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    const { status } = body;

    // Validate status value
    if (status !== "approved" && status !== "rejected" && status !== "accepted") {
      return NextResponse.json(
        { error: "Invalid status value" },
        { status: 400 }
      );
    }

    // Update staff application status
    const updatedApplication = await (prisma as any).staffApplication.update({
      where: { id },
      data: { 
        status,
        statusUpdatedAt: new Date()
      }
    });

    // Get user email
    const user = await prisma.user.findUnique({
      where: { id: updatedApplication.userId },
      select: { email: true }
    });

    // Format the updated application to match the interface expected by the frontend
    const formattedApplication = {
      id: updatedApplication.id,
      fullName: updatedApplication.fullName,
      email: user?.email || "",
      age: updatedApplication.age,
      location: updatedApplication.location,
      availability: updatedApplication.availability,
      position: updatedApplication.position,
      experience: updatedApplication.experience,
      skills: updatedApplication.skills,
      motivation: updatedApplication.motivation,
      portfolioUrl: updatedApplication.portfolioUrl,
      additionalInfo: updatedApplication.additionalInfo,
      status: updatedApplication.status,
      createdAt: updatedApplication.appliedAt
    };

    return NextResponse.json(formattedApplication);
  } catch (error) {
    console.error("Error updating staff application status:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// Delete staff application
export async function DELETE(request: NextRequest) {
  try {
    // Get the URL to extract the ID from the path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // API Key validation
    const apiKey = request.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // Check if the staff application exists
    const applicationExists = await (prisma as any).staffApplication.findUnique({
      where: { id }
    });

    if (!applicationExists) {
      return NextResponse.json(
        { error: "Staff application not found" },
        { status: 404 }
      );
    }

    // Delete the staff application
    await (prisma as any).staffApplication.delete({
      where: { id }
    });

    return NextResponse.json({ 
      success: true, 
      message: "Staff application deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting staff application:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// Add GET method to fetch a single staff application
export async function GET(request: NextRequest) {
  try {
    // Get the URL to extract the ID from the path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // API Key validation
    const apiKey = request.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // Find the staff application
    const application = await (prisma as any).staffApplication.findUnique({
      where: { id }
    });

    if (!application) {
      return NextResponse.json(
        { error: "Staff application not found" },
        { status: 404 }
      );
    }

    // Get the user's email
    const user = await prisma.user.findUnique({
      where: { id: application.userId },
      select: { email: true }
    });

    // Format the application for the frontend
    const formattedApplication = {
      id: application.id,
      userId: application.userId,
      fullName: application.fullName,
      email: user?.email || "",
      age: application.age,
      location: application.location,
      position: application.position,
      experience: application.experience,
      skills: application.skills,
      motivation: application.motivation,
      availability: application.availability,
      portfolioUrl: application.portfolioUrl,
      additionalInfo: application.additionalInfo,
      status: application.status,
      createdAt: application.appliedAt
    };

    return NextResponse.json(formattedApplication);
  } catch (error) {
    console.error("Error fetching staff application:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 