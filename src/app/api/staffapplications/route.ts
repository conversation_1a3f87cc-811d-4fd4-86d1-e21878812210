import { NextResponse } from "next/server";
import prisma from "@/lib/db";

const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

// Define a local interface for StaffApplication
interface StaffApp {
  id: string;
  userId: string;
  fullName: string;
  age: number;
  location: string;
  availability: string;
  position: string;
  experience: string;
  skills: string;
  motivation: string;
  portfolioUrl?: string | null;
  additionalInfo?: string | null;
  status: string;
  appliedAt: Date;
  statusUpdatedAt?: Date | null;
}

// Define a user interface for type safety
interface User {
  id: string;
  email: string | null;
}

export async function GET(req: Request) {
  try {
    // API Key validation
    const apiKey = req.headers.get("x-api-key");
    
    console.log("Staff API received key:", apiKey ? `${apiKey.slice(0, 5)}...` : "none");
    console.log("Expected API key:", API_KEY ? `${API_KEY.slice(0, 5)}...` : "none");
    
    if (apiKey !== API_KEY) {
      console.warn("Staff API key validation failed", {
        providedKey: apiKey ? `${apiKey.slice(0, 5)}...` : "none",
        expectedKey: API_KEY ? `${API_KEY.slice(0, 5)}...` : "none"
      });
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // Fetch all staff applications
    console.log("Fetching staff applications");
    const applications = await (prisma as any).staffApplication.findMany({
      orderBy: {
        appliedAt: "desc"
      }
    });
    
    console.log(`Found ${applications.length} staff applications`);

    if (applications.length === 0) {
      return NextResponse.json([]);
    }

    // Get all users to look up email addresses
    const users = await prisma.user.findMany({
      where: {
        id: {
          in: applications.map((app: StaffApp) => app.userId)
        }
      },
      select: {
        id: true,
        email: true
      }
    });
    
    console.log(`Found ${users.length} users associated with applications`);

    // Create a lookup map for users
    const userMap = new Map<string, string | null>();
    users.forEach((user: User) => {
      userMap.set(user.id, user.email);
    });

    // Format the applications to match the interface expected by the frontend
    const formattedApplications = applications.map((app: StaffApp) => ({
      id: app.id,
      fullName: app.fullName,
      email: userMap.get(app.userId) || "",
      age: app.age,
      location: app.location,
      availability: app.availability,
      position: app.position,
      experience: app.experience,
      skills: app.skills,
      motivation: app.motivation,
      portfolioUrl: app.portfolioUrl,
      additionalInfo: app.additionalInfo,
      status: app.status,
      createdAt: app.appliedAt
    }));

    return NextResponse.json(formattedApplications);
  } catch (error) {
    console.error("Error fetching staff applications:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 