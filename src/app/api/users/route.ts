/*
 * Official Avehub Code, verified
 * Users API Routes - Handles fetching all users
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextResponse } from "next/server";
import prisma from "@/lib/db";

// [1] GET - Retrieve all users with selected fields
export async function GET() {
  try {
    // [1.1] Fetch all users with necessary fields
    const users = await prisma.user.findMany({
      select: { id: true, name: true, email: true, image: true, admin: true, isDeveloper: true },
    });

    // [1.2] Return the users data
    return NextResponse.json(users);
  } catch (error) {
    // [1.3] Handle and log any errors
    console.error("Error fetching users:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
