/*
 * Official Avehub Code, verified
 * Developer Check API Route - Validates developer status for middleware
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

// [1] POST - Check if user is a developer
export async function POST(request: NextRequest) {
  try {
    // [1.1] Parse request body
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // [1.2] Find user by email and check developer status
    const user = await prisma.user.findUnique({
      where: { email },
      select: { isDeveloper: true }
    });

    // [1.3] Return developer status
    if (!user) {
      return NextResponse.json({ isDeveloper: false });
    }

    return NextResponse.json({ isDeveloper: user.isDeveloper });
  } catch (error) {
    // [1.4] Handle errors
    console.error("Error checking developer status:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
