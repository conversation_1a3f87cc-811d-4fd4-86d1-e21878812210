import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/db";
import { auth } from "@/auth";

export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const sortBy = searchParams.get("sortBy") || "releaseDate"; // Default sort by release date
    const order = searchParams.get("order") || "desc"; // Default order descending
    const search = searchParams.get("search") || "";
    
    // Define the sort configuration
    const orderBy: any = {};
    if (sortBy === "downloads") {
      orderBy.downloads = order === "asc" ? "asc" : "desc";
    } else if (sortBy === "name") {
      orderBy.name = order === "asc" ? "asc" : "desc";
    } else if (sortBy === "releaseDate") {
      orderBy.releaseDate = order === "asc" ? "asc" : "desc";
    } else if (sortBy === "updateDate") {
      orderBy.updateDate = order === "asc" ? "asc" : "desc";
    }

    // Build search condition
    const where: any = {};
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { publisherName: { contains: search, mode: "insensitive" } }
      ];
    }

    // Fetch plugins with search and sort applied
    const plugins = await db.plugin.findMany({
      where,
      orderBy,
      include: {
        _count: {
          select: { comments: true }
        }
      }
    });

    return NextResponse.json(plugins);
  } catch (error) {
    console.error("Error fetching plugins:", error);
    return NextResponse.json(
      { error: "Failed to fetch plugins" },
      { status: 500 }
    );
  }
} 