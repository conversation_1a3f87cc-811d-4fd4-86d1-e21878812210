import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/db";
import { auth } from "@/auth";

// GET a plugin with its comments by ID
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id: pluginId } = await params;

    const plugin = await db.plugin.findUnique({
      where: { id: pluginId },
      include: {
        comments: {
          orderBy: { createdAt: "desc" }
        }
      }
    });

    if (!plugin) {
      return NextResponse.json({ error: "Plugin not found" }, { status: 404 });
    }

    return NextResponse.json(plugin);
  } catch (error) {
    console.error("Error fetching plugin:", error);
    return NextResponse.json({ error: "Failed to fetch plugin" }, { status: 500 });
  }
}

// PATCH to increment download count
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id: pluginId } = await params;

    const updated = await db.plugin.update({
      where: { id: pluginId },
      data: { downloads: { increment: 1 } }
    });

    return NextResponse.json(
      { success: true, downloads: updated.downloads }
    );
  } catch (error) {
    console.error("Error updating download count:", error);
    return NextResponse.json({ error: "Failed to update download count" }, { status: 500 });
  }
}

// DELETE a plugin (admin only)
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { id: pluginId } = await params;

    await db.plugin.delete({ where: { id: pluginId } });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting plugin:", error);
    return NextResponse.json({ error: "Failed to delete plugin" }, { status: 500 });
  }
}
