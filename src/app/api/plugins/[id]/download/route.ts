import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/db";
import path from "path";
import fs from "fs/promises";
import supabase, { initSupabaseStorage } from "@/lib/supabase";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id: pluginId } = await params;

    // Find the plugin
    const plugin = await db.plugin.findUnique({
      where: { id: pluginId }
    });

    if (!plugin) {
      return NextResponse.json({ error: "Plugin not found" }, { status: 404 });
    }

    // Increment download count
    await db.plugin.update({
      where: { id: pluginId },
      data: { downloads: { increment: 1 } }
    });

    // Get file location
    const fileLocation = plugin.fileLocation;
    console.log("File location:", fileLocation);

    // Check if it's a Supabase URL
    if (fileLocation.includes('supabase.co')) {
      try {
        // Initialize Supabase storage
        const initialized = await initSupabaseStorage();
        if (!initialized) {
          console.error("Failed to initialize Supabase storage");
        }
        
        // Ensure Supabase client is initialized
        if (!supabase) {
          throw new Error("Supabase client is not initialized. Check your environment variables.");
        }
        
        // Parse the URL to extract the path
        const url = new URL(fileLocation);
        const pathParts = url.pathname.split('/');
        
        // For Supabase URLs, we need to handle different formats
        let bucket: string = 'plugins';
        let filePath: string = '';
        
        // Check for different URL patterns
        const objectIndex = pathParts.indexOf('object');
        const publicIndex = pathParts.indexOf('public');
        
        if (objectIndex !== -1 && publicIndex !== -1 && publicIndex > objectIndex) {
          // Format: /storage/v1/object/public/{bucket}/{file_path}
          bucket = pathParts[publicIndex + 1]; // Should be 'plugins'
          filePath = pathParts.slice(publicIndex + 2).join('/');
        } else if (publicIndex !== -1) {
          // Alternative format
          const bucketIndex = pathParts.findIndex((part, index) => 
            index > publicIndex && part.length > 0);
          
          if (bucketIndex !== -1) {
            bucket = pathParts[bucketIndex]; 
            filePath = pathParts.slice(bucketIndex + 1).join('/');
          }
        }
        
        // If we couldn't determine the path, use a fallback format
        if (!filePath) {
          // Fallback: try to extract filename and use a default path
          const fileName = plugin.fileLocation.split('/').pop() || 
                          `${plugin.name.replace(/[^a-zA-Z0-9]/g, '-')}-v${plugin.version}.zip`;
          filePath = `public/${fileName}`;
          console.log("Using fallback file path:", filePath);
        } else {
          console.log("Extracted bucket:", bucket, "FilePath:", filePath);
        }
        
        // If bucket is not 'plugins', log a warning but continue with 'plugins'
        if (bucket !== 'plugins') {
          console.warn(`Unusual bucket name detected: ${bucket}, using 'plugins' instead`);
          bucket = 'plugins';
        }
        
        // Download file from Supabase
        const { data, error } = await supabase.storage
          .from(bucket)
          .download(filePath);
          
        if (error || !data) {
          console.error("Error downloading from Supabase:", error);
          throw new Error(`Failed to download file from Supabase: ${error?.message}`);
        }
        
        // Get the file data as array buffer
        const arrayBuffer = await data.arrayBuffer();
        
        // Determine content type
        const contentType = data.type || 'application/octet-stream';
        
        // Extract filename from the path or use plugin name
        const fileName = path.basename(filePath) || `${plugin.name.replace(/[^a-zA-Z0-9]/g, '-')}-v${plugin.version}.zip`;
        
        // Return the file as a response
        return new NextResponse(arrayBuffer, {
          headers: {
            'Content-Type': contentType,
            'Content-Disposition': `attachment; filename="${fileName}"`,
          }
        });
      } catch (error) {
        console.error("Error processing Supabase file:", error);
        return NextResponse.json({ 
          error: "Failed to process file from Supabase storage",
          details: error instanceof Error ? error.message : String(error),
          fileLocation: plugin.fileLocation
        }, { status: 500 });
      }
    } else {
      // Handle local file path (legacy support)
      try {
        // Extract the file path from fileLocation (which should be relative like '/storage/filename')
        const localPath = fileLocation.startsWith('/') 
          ? fileLocation.substring(1) // Remove leading slash
          : fileLocation;
        
        // Construct the absolute path
        const filePath = path.join(process.cwd(), 'public', localPath);
        
        // Check if file exists
        await fs.access(filePath);
        
        // Read the file
        const fileBuffer = await fs.readFile(filePath);
        
        // Determine content type based on extension
        const ext = path.extname(filePath).toLowerCase();
        let contentType = 'application/octet-stream';
        
        // Set common content types based on extension
        if (ext === '.zip') contentType = 'application/zip';
        else if (ext === '.pdf') contentType = 'application/pdf';
        else if (ext === '.png') contentType = 'image/png';
        else if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';

        // Extract filename
        const fileName = path.basename(filePath);
        
        return new NextResponse(fileBuffer, {
          headers: {
            'Content-Type': contentType,
            'Content-Disposition': `attachment; filename="${fileName}"`,
          }
        });
      } catch (error) {
        console.error("Error reading local file:", error);
        return NextResponse.json({ 
          error: "Failed to access local file",
          details: error instanceof Error ? error.message : String(error),
          fileLocation: plugin.fileLocation
        }, { status: 500 });
      }
    }
  } catch (error) {
    console.error("Error handling download:", error);
    return NextResponse.json({ 
      error: "Failed to process download",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 