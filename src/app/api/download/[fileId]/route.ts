/*
 * Download Proxy API Route - Handles CDN downloads
 * Updated for new CDN public access - no API key required for GET requests
 */

import { NextRequest, NextResponse } from "next/server";

// CDN Configuration
const CDN_BASE_URL = "https://cdn.avehubs.com";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await params;

    console.log("📥 Download proxy request for file:", fileId);

    // Fetch file from CDN - no API key required for public read access
    const cdnResponse = await fetch(`${CDN_BASE_URL}/f/${fileId}`, {
      // No headers needed for public access
    });

    console.log("📤 CDN Response:", {
      status: cdnResponse.status,
      statusText: cdnResponse.statusText,
      ok: cdnResponse.ok,
      contentType: cdnResponse.headers.get('content-type'),
      contentLength: cdnResponse.headers.get('content-length')
    });

    if (!cdnResponse.ok) {
      const errorText = await cdnResponse.text();
      console.error("❌ CDN download failed:", {
        status: cdnResponse.status,
        statusText: cdnResponse.statusText,
        errorText: errorText.substring(0, 200),
        fileId
      });

      if (cdnResponse.status === 404) {
        return NextResponse.json({ error: "File not found" }, { status: 404 });
      } else if (cdnResponse.status === 401 || cdnResponse.status === 403) {
        return NextResponse.json({ error: "Access denied" }, { status: 403 });
      }

      return NextResponse.json({ error: "Download failed" }, { status: 500 });
    }

    // Get file content as stream
    const fileBuffer = await cdnResponse.arrayBuffer();

    // Get content type and filename from CDN response
    const contentType = cdnResponse.headers.get('content-type') || 'application/octet-stream';
    const contentDisposition = cdnResponse.headers.get('content-disposition');

    // Create response with proper headers
    const response = new NextResponse(fileBuffer);

    response.headers.set('Content-Type', contentType);
    response.headers.set('Content-Length', fileBuffer.byteLength.toString());

    if (contentDisposition) {
      response.headers.set('Content-Disposition', contentDisposition);
    } else {
      // Set a default filename if not provided
      response.headers.set('Content-Disposition', `attachment; filename="${fileId}"`);
    }

    // Add cache headers
    response.headers.set('Cache-Control', 'public, max-age=31536000'); // 1 year
    response.headers.set('ETag', cdnResponse.headers.get('etag') || `"${fileId}"`);

    console.log("✅ Download proxy successful for file:", fileId);

    return response;

  } catch (error) {
    console.error("Error in download proxy:", error);
    return NextResponse.json({ error: "Download failed" }, { status: 500 });
  }
}
