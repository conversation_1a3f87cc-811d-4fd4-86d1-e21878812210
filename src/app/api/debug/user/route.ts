/*
 * Debug API Route - Check current user status and developer permissions
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    return NextResponse.json({
      success: true,
      data: {
        hasSession: !!session,
        user: session?.user ? {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
          isDeveloper: session.user.isDeveloper,
          image: session.user.image
        } : null,
        sessionExpires: session?.expires
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Debug user API error:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to get user info",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
