import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

export async function DELETE(req: NextRequest) {
  try {
    const apiKey = req.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      return NextResponse.json({ error: "❌ Unauthorized: Invalid API Key." }, { status: 403 });
    }
    const id = req.nextUrl.searchParams.get("id");

    if (!id?.trim()) {
      return NextResponse.json({ error: "⚠️ Announcement ID is required." }, { status: 400 });
    }

    const existingAnnouncement = await prisma.announcement.findUnique({ where: { id } });

    if (!existingAnnouncement) {
      return NextResponse.json({ error: "❌ Announcement not found." }, { status: 404 });
    }

    await prisma.announcement.delete({ where: { id } });

    return NextResponse.json({ message: "✅ Announcement deleted successfully!" }, { status: 200 });
  } catch (error) {
    console.error("Error deleting announcement:", error);
    return NextResponse.json({ error: "❌ Internal Server Error" }, { status: 500 });
  }
}
