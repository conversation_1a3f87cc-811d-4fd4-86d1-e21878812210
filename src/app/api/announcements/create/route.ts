import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

const API_KEY = process.env.NEXT_PUBLIC_API_KEY || ""; 

export async function POST(req: NextRequest) {
  try {
    const apiKey = req.headers.get("x-api-key");

    if (apiKey !== API_KEY) {
      return NextResponse.json({ error: "Unauthorized: Invalid API Key." }, { status: 403 });
    }

    const body = await req.json();
    const { title, description, footer } = body;

    if (!title?.trim() || !description?.trim() || !footer?.trim()) {
      return NextResponse.json({ error: "Title, description, and footer are required." }, { status: 400 });
    }

    const announcement = await prisma.announcement.create({
      data: { title, description, footer },
    });

    return NextResponse.json({ announcement }, { status: 201 });
  } catch (error) {
    console.error("Error creating announcement:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
