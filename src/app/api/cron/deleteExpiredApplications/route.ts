import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
const COOLDOWN_MS = 30 * 24 * 60 * 60 * 1000;

export async function GET() {
  try {
    const now = new Date();
    const cooldownDate = new Date(now.getTime() - COOLDOWN_MS);

    const deleted = await prisma.partnershipApplication.deleteMany({
      where: {
        status: { in: ["accepted", "rejected"] },
        statusUpdatedAt: { lt: cooldownDate },
      },
    });

    return NextResponse.json({ success: true, deleted: deleted.count });
  } catch (error) {
    console.error("❌ Error deleting expired applications:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
