/*
 * Asset Proxy API Route - Handles CDN asset access
 * Updated for new CDN public access - no API key required for GET requests
 */

import { NextRequest, NextResponse } from "next/server";

// CDN Configuration
const CDN_BASE_URL = "https://cdn.avehubs.com";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await params;

    console.log("🖼️ Asset proxy request for file:", fileId);

    // Fetch asset from CDN - no API key required for public read access
    const cdnResponse = await fetch(`${CDN_BASE_URL}/f/${fileId}`, {
      // No headers needed for public access
    });

    console.log("📤 CDN Asset Response:", {
      status: cdnResponse.status,
      statusText: cdnResponse.statusText,
      ok: cdnResponse.ok,
      contentType: cdnResponse.headers.get('content-type'),
      contentLength: cdnResponse.headers.get('content-length')
    });

    if (!cdnResponse.ok) {
      const errorText = await cdnResponse.text();
      console.error("❌ CDN asset fetch failed:", {
        status: cdnResponse.status,
        statusText: cdnResponse.statusText,
        errorText: errorText.substring(0, 200),
        fileId
      });

      if (cdnResponse.status === 404) {
        return NextResponse.json({ error: "Asset not found" }, { status: 404 });
      } else if (cdnResponse.status === 401 || cdnResponse.status === 403) {
        return NextResponse.json({ error: "Access denied" }, { status: 403 });
      }

      return NextResponse.json({ error: "Asset fetch failed" }, { status: 500 });
    }

    // Get asset content as stream
    const assetBuffer = await cdnResponse.arrayBuffer();

    // Get content type from CDN response
    const contentType = cdnResponse.headers.get('content-type') || 'application/octet-stream';

    // Create response with proper headers for assets
    const response = new NextResponse(assetBuffer);

    response.headers.set('Content-Type', contentType);
    response.headers.set('Content-Length', assetBuffer.byteLength.toString());

    // Add aggressive cache headers for assets (images, etc.)
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year, immutable
    response.headers.set('ETag', cdnResponse.headers.get('etag') || `"${fileId}"`);

    // Add CORS headers for cross-origin asset access
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type');

    console.log("✅ Asset proxy successful for file:", fileId);

    return response;

  } catch (error) {
    console.error("Error in asset proxy:", error);
    return NextResponse.json({ error: "Asset fetch failed" }, { status: 500 });
  }
}
