/*
 * Official Avehub Code, verified
 * Email Sending API - Handles outgoing email operations using Zoho SMTP
 * Any unauthorized modifications will invalidate service warranty
 */
import { NextRequest, NextResponse } from "next/server";
import nodemailer from "nodemailer";

// Direct values 
const EMAIL_USER = process.env.ZOHO_EMAIL_USER || "";
const EMAIL_PASSWORD = process.env.ZOHO_EMAIL_PASSWORD || ""; 
const API_KEY = process.env.NEXT_PUBLIC_API_KEY || ""; 

// Create reusable transporter
const transporter = nodemailer.createTransport({
  host: "smtp.zoho.com",
  port: 465,
  secure: true,
  auth: {
    user: EMAIL_USER,
    pass: EMAIL_PASSWORD,
  },
});

// Verify transporter on startup
transporter.verify((error) => {
  if (error) {
    console.error('SMTP Connection Error:', error);
  } else {
    console.log('SMTP Connection Successfully Established');
  }
});

export async function POST(req: NextRequest) {
  try {
    // API Key validation
    const apiKey = req.headers.get("x-api-key");
    
    console.log("Mail API received key:", apiKey ? `${apiKey.slice(0, 5)}...` : "none");
    console.log("Expected API key:", API_KEY ? `${API_KEY.slice(0, 5)}...` : "none");
    
    if (apiKey !== API_KEY) {
      console.warn("Email API key validation failed", {
        providedKey: apiKey ? `${apiKey.slice(0, 5)}...` : "none",
        expectedKey: API_KEY ? `${API_KEY.slice(0, 5)}...` : "none"
      });
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // Parse request body
    const { email, title, description, footer } = await req.json();

    console.log(`Sending email to ${email} with title: ${title}`);

    // Basic validation
    if (!email?.trim() || !title?.trim()) {
      return NextResponse.json(
        { error: "Email and title are required." },
        { status: 400 }
      );
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: "Invalid email format." },
        { status: 400 }
      );
    }

    // Create HTML content with basic security measures
    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
      </head>
      <body style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #333; margin: 0 0 15px 0;">${title}</h2>
            ${description ? 
              `<div style="color: #555; line-height: 1.6; margin: 20px 0;">${description}</div>` 
              : ''}
          </div>
          ${footer ? 
            `<div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 14px;">
              ${footer}
            </div>` 
            : ''}
          <div style="text-align: center; margin-top: 20px; color: #999; font-size: 12px;">
            © ${new Date().getFullYear()} AveHub. All rights reserved.
          </div>
        </div>
      </body>
      </html>
    `;

    // Send email
    const info = await transporter.sendMail({
      from: {
        name: "AveHub",
        address: EMAIL_USER
      },
      to: email,
      subject: title,
      html: htmlContent,
      headers: {
        'X-Priority': '1',
        'X-MSMail-Priority': 'High'
      }
    });

    console.log(`Email sent successfully to ${email}, messageId: ${info.messageId}`);

    return NextResponse.json(
      { 
        success: true, 
        messageId: info.messageId,
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Error sending email:", error);
    
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    
    return NextResponse.json(
      { 
        error: "Failed to send email",
        details: errorMessage
      },
      { status: 500 }
    );
  }
}
