import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get("userId");

  if (!userId) {
    return NextResponse.json({ error: "Missing userId" }, { status: 400 });
  }

  try {
    const application = await prisma.partnershipApplication.findFirst({
      where: { userId },
    });
    
    if (!application) {
      return NextResponse.json({ application: null });
    }
    
    return NextResponse.json({ application });
  } catch (error) {
    console.error("Error retrieving application:", error);
    return NextResponse.json({ error: "Error retrieving application" }, { status: 500 });
  }
}
