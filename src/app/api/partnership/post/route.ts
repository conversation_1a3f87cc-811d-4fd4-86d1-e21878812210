import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
const COOLDOWN_MS = 30 * 24 * 60 * 60 * 1000; 
const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

export async function POST(req: Request) {
  const apiKey = req.headers.get("x-api-key");
  if (apiKey !== API_KEY) {
    return NextResponse.json(
      { error: "❌ Unauthorized: Invalid API Key." },
      { status: 403 }
    );
  }

  const { userId, username, companyName, websiteUrl, reason, experience, additional } =
    await req.json();

  if (![userId, username, companyName, websiteUrl, reason, experience].every(Boolean)) {
    return NextResponse.json(
      { error: "All fields are required." },
      { status: 400 }
    );
  }

  const existingApplication = await prisma.partnershipApplication.findFirst({
    where: { userId },
  });

  if (existingApplication) {
    if (existingApplication.status === "pending") {
      return NextResponse.json(
        { error: "Your application is under review." },
        { status: 400 }
      );
    }
    if (existingApplication.status === "accepted") {
      return NextResponse.json(
        { error: "Your application was accepted. No need to reapply." },
        { status: 400 }
      );
    }

    const now = Date.now();
    const appliedAt = new Date(existingApplication.appliedAt).getTime();
    if (now - appliedAt < COOLDOWN_MS) {
      const daysLeft = Math.ceil((COOLDOWN_MS - (now - appliedAt)) / (1000 * 60 * 60 * 24));
      return NextResponse.json(
        { error: `You can apply again in ${daysLeft} days.` },
        { status: 400 }
      );
    }

    await prisma.partnershipApplication.delete({ where: { id: existingApplication.id } });
  }

  const application = await prisma.partnershipApplication.create({
    data: {
      userId,
      username,
      companyName,
      websiteUrl,
      reason,
      experience,
      additional,
      status: "pending",
      appliedAt: new Date(),
    },
  });

  return NextResponse.json(
    { message: "Application submitted!", application },
    { status: 201 }
  );
}
