/*
 * Official Avehub Code, verified
 * Apps API Routes - Handles CRUD operations for applications
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/db";

// [1] GET - Retrieve apps (public route with filtering)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const order = searchParams.get("order") || "desc";
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : undefined;
    const featured = searchParams.get("featured");
    const developerId = searchParams.get("developerId");
    const isAdmin = searchParams.get("admin") === "true";

    // Set default status based on context
    let status = searchParams.get("status");
    if (!status) {
      if (isAdmin) {
        status = "ALL"; // Admin sees all by default
      } else if (developerId) {
        status = "ALL"; // Developers see all their own apps by default
      } else {
        status = "APPROVED"; // Public sees only approved by default
      }
    }

    // [1.0] Check admin access for admin queries
    if (isAdmin) {
      const session = await auth();
      if (!session?.user?.admin) {
        return NextResponse.json({ error: "Admin access required" }, { status: 403 });
      }
    }

    // [1.1] Build where clause
    const where: any = {};

    // Status filter logic
    if (isAdmin) {
      // Admin can see all statuses
      if (status !== "ALL") {
        where.status = status;
      }
    } else if (developerId) {
      // Developers can see all their own apps regardless of status
      if (status !== "ALL") {
        where.status = status;
      }
    } else {
      // Public users only see approved apps
      where.status = "APPROVED";
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { tags: { has: search } }
      ];
    }

    if (category) {
      where.category = category;
    }

    if (featured !== null && featured !== undefined) {
      where.featured = featured === "true";
    }

    if (developerId) {
      where.developerId = developerId;
    }

    // [1.2] Build orderBy clause
    const orderBy: any = {};
    orderBy[sortBy] = order;

    // [1.3] Fetch apps with developer info
    const apps = await prisma.app.findMany({
      where,
      orderBy,
      include: {
        developer: {
          select: { id: true, name: true, image: true }
        },
        _count: {
          select: { comments: true }
        }
      },
      ...(limit && { take: limit })
    });

    return NextResponse.json(apps);
  } catch (error) {
    console.error("Error fetching apps:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// [2] POST - Create new app (developer only)
export async function POST(request: NextRequest) {
  try {
    // [2.1] Check authentication and developer status
    const session = await auth();
    if (!session?.user?.isDeveloper) {
      return NextResponse.json({ error: "Developer access required" }, { status: 403 });
    }

    // [2.2] Parse request body
    const data = await request.json();
    const {
      name,
      description,
      shortDescription,
      version,
      category,
      tags,
      downloadUrl,
      fileSize,
      screenshots,
      iconUrl,
      bannerUrl,
      minVersion,
      maxVersion,
      website,
      supportEmail,
      changelog,
      visibility
    } = data;

    // [2.3] Validate required fields
    if (!name || !description || !version || !category || !downloadUrl || !fileSize) {
      return NextResponse.json({
        error: "Missing required fields: name, description, version, category, downloadUrl, fileSize"
      }, { status: 400 });
    }

    // [2.4] Create new app
    const app = await prisma.app.create({
      data: {
        developerId: session.user.id,
        name,
        description,
        shortDescription,
        version,
        category,
        tags: tags || [],
        downloadUrl,
        fileSize: parseInt(fileSize),
        screenshots: screenshots || [],
        iconUrl,
        bannerUrl,
        minVersion,
        maxVersion,
        website,
        supportEmail,
        changelog,
        visibility: visibility || "PUBLIC"
      },
      include: {
        developer: {
          select: { id: true, name: true, image: true }
        }
      }
    });

    return NextResponse.json(app, { status: 201 });
  } catch (error) {
    console.error("Error creating app:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
