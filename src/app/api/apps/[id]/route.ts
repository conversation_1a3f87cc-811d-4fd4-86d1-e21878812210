/*
 * Official Avehub Code, verified
 * Individual App API Routes - Handles single app operations
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/db";

// [1] GET - Retrieve single app
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // [1.1] Fetch app with all related data
    const app = await prisma.app.findUnique({
      where: { id },
      include: {
        developer: {
          select: { id: true, name: true, image: true }
        },
        comments: {
          include: {
            user: {
              select: { id: true, name: true, image: true }
            }
          },
          orderBy: { createdAt: "desc" }
        },
        versions: {
          orderBy: { createdAt: "desc" }
        },
        _count: {
          select: { comments: true }
        }
      }
    });

    if (!app) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    return NextResponse.json(app);
  } catch (error) {
    console.error("Error fetching app:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// [2] PATCH - Update app (developer only, own apps)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // [2.1] Check authentication and developer status
    const session = await auth();
    if (!session?.user?.isDeveloper) {
      return NextResponse.json({ error: "Developer access required" }, { status: 403 });
    }

    const { id } = await params;

    // [2.2] Check if app exists and belongs to user
    const existingApp = await prisma.app.findUnique({
      where: { id },
      select: { developerId: true }
    });

    if (!existingApp) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    if (existingApp.developerId !== session.user.id) {
      return NextResponse.json({ error: "You can only edit your own apps" }, { status: 403 });
    }

    // [2.3] Parse request body
    const data = await request.json();
    const updateData: any = {};

    // [2.4] Only update provided fields
    const allowedFields = [
      'name', 'description', 'shortDescription', 'version', 'category', 'tags',
      'downloadUrl', 'fileSize', 'screenshots', 'iconUrl', 'bannerUrl',
      'minVersion', 'maxVersion', 'website', 'supportEmail', 'changelog', 'visibility'
    ];

    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        if (field === 'fileSize') {
          updateData[field] = parseInt(data[field]);
        } else {
          updateData[field] = data[field];
        }
      }
    });

    // [2.5] Update app
    const updatedApp = await prisma.app.update({
      where: { id },
      data: updateData,
      include: {
        developer: {
          select: { id: true, name: true, image: true }
        }
      }
    });

    return NextResponse.json(updatedApp);
  } catch (error) {
    console.error("Error updating app:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// [3] DELETE - Delete app (developer only, own apps)
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // [3.1] Check authentication and developer status
    const session = await auth();
    if (!session?.user?.isDeveloper) {
      return NextResponse.json({ error: "Developer access required" }, { status: 403 });
    }

    const { id } = await params;

    // [3.2] Check if app exists and belongs to user
    const existingApp = await prisma.app.findUnique({
      where: { id },
      select: { developerId: true }
    });

    if (!existingApp) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    if (existingApp.developerId !== session.user.id) {
      return NextResponse.json({ error: "You can only delete your own apps" }, { status: 403 });
    }

    // [3.3] Delete app (cascade will handle related records)
    await prisma.app.delete({
      where: { id }
    });

    return NextResponse.json({ message: "App deleted successfully" });
  } catch (error) {
    console.error("Error deleting app:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
