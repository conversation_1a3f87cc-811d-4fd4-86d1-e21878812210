/**
 * App Deletion API Route
 * Handles complete app deletion including database and CDN cleanup
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/db";
import { deleteCDNFiles } from "@/utils/cdnUtils";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`🗑️ App deletion request for ID: ${id}`);

    // [1] Authentication check
    const session = await auth();
    if (!session?.user?.id) {
      console.log("❌ Unauthorized deletion attempt");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // [2] Fetch app with all related data
    const app = await prisma.app.findUnique({
      where: { id },
      include: {
        developer: true,
        comments: true,
        versions: true
      }
    });

    if (!app) {
      console.log(`❌ App not found: ${id}`);
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    // [3] Authorization check - only app developer or admin can delete
    const isOwner = app.developerId === session.user.id;
    const isAdmin = session.user.admin === true;

    if (!isOwner && !isAdmin) {
      console.log(`❌ Unauthorized deletion attempt by user ${session.user.id} for app ${id}`);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log(`✅ Deletion authorized for app: ${app.name} by ${isAdmin ? 'admin' : 'owner'}`);

    // [4] Collect all CDN files to delete
    const filesToDelete: string[] = [];

    // Add main app file
    if (app.downloadUrl) {
      filesToDelete.push(app.downloadUrl);
    }

    // Add icon
    if (app.iconUrl) {
      filesToDelete.push(app.iconUrl);
    }

    // Add banner
    if (app.bannerUrl) {
      filesToDelete.push(app.bannerUrl);
    }

    // Add screenshots
    if (app.screenshots && app.screenshots.length > 0) {
      filesToDelete.push(...app.screenshots);
    }

    console.log(`📁 Found ${filesToDelete.length} files to delete from CDN`);

    // [5] Delete from database first (with transaction)
    await prisma.$transaction(async (tx) => {
      // Delete comments
      await tx.appComment.deleteMany({
        where: { appId: id }
      });

      // Delete app versions
      await tx.appVersion.deleteMany({
        where: { appId: id }
      });

      // Delete the app
      await tx.app.delete({
        where: { id }
      });
    });

    console.log(`✅ App deleted from database: ${app.name}`);

    // [6] Delete files from CDN (non-blocking)
    if (filesToDelete.length > 0) {
      // Don't await this - let it run in background
      deleteCDNFiles(filesToDelete).then((result) => {
        console.log(`🗑️ CDN cleanup completed for app ${app.name}: ${result.success} successful, ${result.failed} failed`);
      }).catch((error) => {
        console.error(`❌ CDN cleanup failed for app ${app.name}:`, error);
      });
    }

    // [7] Return success response
    return NextResponse.json({
      success: true,
      message: "App deleted successfully",
      deletedApp: {
        id: app.id,
        name: app.name,
        filesScheduledForDeletion: filesToDelete.length
      }
    });

  } catch (error) {
    console.error("❌ App deletion error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
