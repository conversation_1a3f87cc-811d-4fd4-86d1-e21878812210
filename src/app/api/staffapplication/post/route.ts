import { NextResponse } from "next/server";
import prisma from "@/lib/db";
import { auth } from "@/auth";

const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

// Function to send confirmation email
const sendConfirmationEmail = async (userEmail: string, fullName: string, position: string) => {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/mail/sender`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": process.env.NEXT_PUBLIC_API_KEY || "",
      },
      body: JSON.stringify({
        email: userEmail,
        title: "Staff Application Received",
        description: `
          <p>Dear ${fullName},</p>
          <p>Thank you for submitting your application for the ${position} position at AveHub!</p>
          <p>We have received your information and our team will review your application carefully. Here's what happens next:</p>
          <ol>
            <li>Our HR team will review your qualifications and experience.</li>
            <li>Selected candidates will be contacted for further steps in the process.</li>
            <li>You'll receive an update on the status of your application within the next 1-2 weeks.</li>
          </ol>
          <p>In the meantime, you can check the status of your application by logging into your AveHub account.</p>
          <p>We appreciate your interest in joining our team!</p>
        `,
        footer: "If you have any questions about your application, please contact our HR <NAME_EMAIL>."
      }),
    });

    if (!response.ok) {
      console.error("Failed to send confirmation email", await response.text());
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error sending confirmation email:", error);
    return false;
  }
};

export async function POST(req: Request) {
  try {
    // API Key validation
    const apiKey = req.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // Parse request body
    const {
      userId,
      fullName,
      age,
      location,
      availability,
      position,
      experience,
      skills,
      motivation,
      portfolioUrl,
      additionalInfo
    } = await req.json();

    // Validate required fields
    if (!userId || !fullName || !location || !availability || !position || !experience || !skills || !motivation) {
      return NextResponse.json(
        { error: "Missing required fields." },
        { status: 400 }
      );
    }

    // Age validation
    if (age !== undefined && (typeof age !== 'number' || age < 14 || age > 100)) {
      return NextResponse.json(
        { error: "Age must be a number between 14 and 100." },
        { status: 400 }
      );
    }

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found." },
        { status: 404 }
      );
    }

    // Check if user already has an application
    const existingApplication = await (prisma as any).staffApplication.findUnique({
      where: { userId },
    });

    if (existingApplication) {
      return NextResponse.json(
        { error: "You already have a staff application submitted." },
        { status: 409 }
      );
    }

    // Create staff application
    const application = await (prisma as any).staffApplication.create({
      data: {
        userId,
        fullName,
        age,
        location,
        availability,
        position,
        experience,
        skills,
        motivation,
        portfolioUrl: portfolioUrl || "",
        additionalInfo: additionalInfo || "",
        status: "pending",
        appliedAt: new Date(),
      },
    });

    // Send confirmation email if we have a user email
    if (application && user.email) {
      // Send confirmation email in the background (don't await)
      sendConfirmationEmail(user.email, fullName, position)
        .then(success => {
          if (success) {
            console.log(`Confirmation email sent to ${user.email}`);
          } else {
            console.error(`Failed to send confirmation email to ${user.email}`);
          }
        })
        .catch(error => {
          console.error("Error in email sending process:", error);
        });
    }

    return NextResponse.json({
      message: "Staff application submitted successfully!",
      application,
    });
  } catch (error) {
    console.error("Error submitting staff application:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 