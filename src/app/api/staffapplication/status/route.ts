import { NextResponse } from "next/server";
import prisma from "@/lib/db";

export async function GET(req: Request) {
  try {
    // Get the user ID from the query string
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { error: "Missing userId parameter" },
        { status: 400 }
      );
    }

    // Find the user's staff application
    const application = await (prisma as any).staffApplication.findUnique({
      where: { userId },
    });

    if (!application) {
      return NextResponse.json(
        { application: null },
        { status: 200 }  // Return 200 with null application to indicate no application exists
      );
    }

    return NextResponse.json({ application });
  } catch (error) {
    console.error("Error retrieving staff application:", error);
    return NextResponse.json(
      { error: "Failed to retrieve application status" },
      { status: 500 }
    );
  }
} 