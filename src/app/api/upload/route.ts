/*
 * Official Avehub Code, verified
 * File Upload API Route - Handles Custom CDN uploads
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";

// Configure route for large file uploads
export const runtime = 'nodejs';
export const maxDuration = 60; // 60 seconds max execution time (Vercel hobby plan limit)

// [1] CDN Configuration
const CDN_BASE_URL = "https://cdn.avehubs.com";
const CDN_API_KEY = process.env.CDN_API_KEY || "cccd3cd6a25cb4b40f1626ebfb0eae4973b8f52457e726b692332b7078ffa078";

// [2] POST - Upload file to Custom CDN
export async function POST(request: NextRequest) {
  try {
    console.log("🚀 Upload API called");

    // [2.1] Check authentication and developer status
    const session = await auth();
    console.log("👤 Session:", {
      userId: session?.user?.id,
      email: session?.user?.email,
      isDeveloper: session?.user?.isDeveloper,
      hasSession: !!session
    });

    if (!session?.user?.isDeveloper) {
      console.log("❌ Developer access denied - returning 403");
      return NextResponse.json({
        error: "Developer access required",
        debug: {
          hasSession: !!session,
          userId: session?.user?.id,
          isDeveloper: session?.user?.isDeveloper
        }
      }, { status: 403 });
    }

    console.log("✅ Developer access granted");

    // [2.2] Parse form data with enhanced error handling for large files
    let formData;
    let file;
    let uploadType;

    try {
      formData = await request.formData();
      file = formData.get("file") as File;
      uploadType = formData.get("uploadType") as string; // 'app', 'screenshot', 'icon', 'banner'
    } catch (formDataError) {
      console.error("❌ Failed to parse form data:", formDataError);
      return NextResponse.json({
        error: "Failed to parse upload data",
        debug: {
          error: formDataError instanceof Error ? formDataError.message : "Unknown error",
          suggestion: "This may happen with very large files or corrupted uploads"
        }
      }, { status: 400 });
    }

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // [2.3] Validate file type and size
    const maxSize = uploadType === 'app' ? 500 * 1024 * 1024 : 10 * 1024 * 1024; // 500MB for apps, 10MB for images

    if (file.size > maxSize) {
      return NextResponse.json({
        error: `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
      }, { status: 400 });
    }

    // [2.4] Validate file types based on CDN supported types
    const allowedTypes = {
      app: ['.jar', '.exe', '.dmg', '.deb', '.rpm', '.zip', '.tar.gz', '.msi', '.apk', '.ipa'],
      screenshot: ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
      icon: ['.jpg', '.jpeg', '.png', '.webp', '.svg'],
      banner: ['.jpg', '.jpeg', '.png', '.webp']
    };

    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const validTypes = allowedTypes[uploadType as keyof typeof allowedTypes] || [];

    if (!validTypes.includes(fileExtension)) {
      return NextResponse.json({
        error: `Invalid file type. Allowed: ${validTypes.join(', ')}`
      }, { status: 400 });
    }

    // [2.5] Prepare form data for CDN upload
    const cdnFormData = new FormData();
    cdnFormData.append("file", file);

    console.log("📤 Preparing CDN upload:", {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadType,
      cdnUrl: `${CDN_BASE_URL}/api/upload`,
      apiKeyLength: CDN_API_KEY.length,
      apiKeyPrefix: CDN_API_KEY.substring(0, 8) + "..."
    });

    // Check if file is too large for single upload (>50MB)
    const isLargeFile = file.size > 50 * 1024 * 1024; // 50MB threshold

    if (isLargeFile) {
      console.warn(`⚠️ Large file detected (${(file.size / 1024 / 1024).toFixed(2)} MB). Using chunked upload strategy.`);

      // For very large files, return an error suggesting compression or smaller files
      if (file.size > 200 * 1024 * 1024) { // 200MB
        return NextResponse.json({
          error: "File too large for upload",
          debug: {
            fileSize: file.size,
            fileSizeMB: (file.size / 1024 / 1024).toFixed(2),
            maxSizeMB: 200,
            suggestion: "Please compress your file or split it into smaller parts. Files over 200MB are not supported due to infrastructure limitations.",
            vercelLimit: "Vercel hobby plan has strict timeout and memory limits"
          }
        }, { status: 413 });
      }
    }

    // [2.6] For large files, provide immediate feedback about potential issues
    if (isLargeFile) {
      console.log(`🔄 Attempting upload for large file: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
      console.warn(`⚠️ Large file warning: This file may fail due to Vercel's 60-second timeout limit and CDN processing constraints.`);

      // For files over 60MB, strongly recommend compression
      if (file.size > 60 * 1024 * 1024) {
        console.error(`❌ File too large: ${(file.size / 1024 / 1024).toFixed(2)} MB exceeds recommended 60MB limit for reliable uploads.`);
        return NextResponse.json({
          error: "File too large for reliable upload",
          debug: {
            fileSize: file.size,
            fileSizeMB: (file.size / 1024 / 1024).toFixed(2),
            recommendedMaxMB: 60,
            suggestion: "Please compress your file to under 60MB. Large executable files can often be compressed significantly using tools like 7-Zip or WinRAR.",
            technicalReason: "Vercel hobby plan has a 60-second timeout limit, and files over 60MB often exceed this limit during upload and processing."
          }
        }, { status: 413 });
      }
    }

    // [2.7] Use existing form data for CDN upload

    // [2.8] Upload to Custom CDN with timeout optimized for Vercel limits
    const controller = new AbortController();
    // Set timeout based on file size but within Vercel's 60-second function limit
    // Large files get 45 seconds, smaller files get 30 seconds
    const timeoutDuration = isLargeFile ? 45 * 1000 : 30 * 1000;
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

    console.log(`⏱️ Upload timeout set to ${timeoutDuration / 1000} seconds for ${(file.size / 1024 / 1024).toFixed(2)} MB file`);

    try {
      const uploadResponse = await fetch(`${CDN_BASE_URL}/api/upload`, {
        method: "POST",
        headers: {
          "X-API-Key": CDN_API_KEY,
          // Add explicit headers for large file handling
          "Accept": "application/json",
          "Cache-Control": "no-cache",
        },
        body: cdnFormData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log("📥 CDN Response:", {
        status: uploadResponse.status,
        statusText: uploadResponse.statusText,
        ok: uploadResponse.ok,
        headers: Object.fromEntries(uploadResponse.headers.entries())
      });

      if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error("❌ CDN upload failed:", {
        status: uploadResponse.status,
        statusText: uploadResponse.statusText,
        errorText: errorText.substring(0, 500), // Show more error text
        apiKeyUsed: CDN_API_KEY.substring(0, 8) + "...",
        url: `${CDN_BASE_URL}/api/upload`,
        contentType: uploadResponse.headers.get('content-type'),
        fileName: file.name,
        fileSize: file.size,
        uploadType
      });

      // Handle specific CDN error codes
      if (uploadResponse.status === 401) {
        return NextResponse.json({
          error: "CDN authentication failed",
          debug: { status: 401, apiKeyPrefix: CDN_API_KEY.substring(0, 8) + "..." }
        }, { status: 500 });
      } else if (uploadResponse.status === 403) {
        return NextResponse.json({
          error: "CDN access forbidden",
          debug: { status: 403, errorText, apiKeyPrefix: CDN_API_KEY.substring(0, 8) + "..." }
        }, { status: 500 });
      } else if (uploadResponse.status === 413) {
        return NextResponse.json({
          error: "File too large for CDN",
          debug: { status: 413, fileSize: file.size }
        }, { status: 400 });
      } else if (uploadResponse.status === 429) {
        return NextResponse.json({
          error: "CDN rate limit exceeded. Please try again later.",
          debug: { status: 429 }
        }, { status: 429 });
      }

      return NextResponse.json({
        error: "CDN upload failed",
        debug: {
          status: uploadResponse.status,
          statusText: uploadResponse.statusText,
          errorText: errorText.substring(0, 200) + "..."
        }
      }, { status: 500 });
    }

    let cdnResult;
    let responseText = "";
    try {
      responseText = await uploadResponse.text();
      console.log("📄 CDN Response details:", {
        status: uploadResponse.status,
        contentType: uploadResponse.headers.get('content-type'),
        contentLength: uploadResponse.headers.get('content-length'),
        responseLength: responseText.length,
        responsePreview: responseText.substring(0, 500)
      });

      // Check if response looks like HTML (error page) instead of JSON
      if (responseText.trim().startsWith('<') || responseText.includes('<!DOCTYPE') || responseText.includes('Request En')) {
        console.error("❌ CDN returned HTML/error page instead of JSON - likely a server error");

        // Special handling for the "Request En..." error pattern
        const isRequestError = responseText.includes('Request En');

        return NextResponse.json({
          error: isRequestError
            ? "CDN server error - request processing failed"
            : "CDN server error - received HTML response instead of JSON",
          debug: {
            status: uploadResponse.status,
            contentType: uploadResponse.headers.get('content-type'),
            responsePreview: responseText.substring(0, 300) + "...",
            fileSize: file.size,
            fileName: file.name,
            uploadType,
            possibleCause: isRequestError
              ? "CDN server likely timed out or ran out of memory processing large file"
              : "Server timeout, memory limit, or internal error for large file",
            suggestion: file.size > 50 * 1024 * 1024
              ? "Try compressing the file to reduce size, or split into smaller parts"
              : "This appears to be a server-side issue. Please try again or contact support."
          }
        }, { status: 500 });
      }

      // Check if response is empty
      if (!responseText.trim()) {
        console.error("❌ CDN returned empty response");
        return NextResponse.json({
          error: "CDN returned empty response",
          debug: {
            status: uploadResponse.status,
            contentType: uploadResponse.headers.get('content-type'),
            fileSize: file.size,
            fileName: file.name,
            uploadType
          }
        }, { status: 500 });
      }

      cdnResult = JSON.parse(responseText);
    } catch (parseError) {
      console.error("❌ Failed to parse CDN response as JSON:", {
        error: parseError,
        responseLength: responseText?.length || 0,
        responseStart: responseText?.substring(0, 100) || "No response",
        fileSize: file.size,
        fileName: file.name
      });

      return NextResponse.json({
        error: "CDN returned invalid JSON response",
        debug: {
          status: uploadResponse.status,
          contentType: uploadResponse.headers.get('content-type'),
          responsePreview: responseText ? responseText.substring(0, 300) + "..." : "No response text",
          parseError: parseError instanceof Error ? parseError.message : "Unknown parse error",
          fileSize: file.size,
          fileName: file.name,
          uploadType,
          suggestion: "This often happens with large files due to server timeouts or memory limits"
        }
      }, { status: 500 });
    }

    if (!cdnResult.success) {
      console.error("CDN upload unsuccessful:", cdnResult);
      return NextResponse.json({
        error: "CDN upload failed",
        debug: cdnResult
      }, { status: 500 });
    }

    // [2.7] Return upload result in compatible format
    const result = cdnResult.data;

    return NextResponse.json({
      url: result.cdn_url, // Use CDN URL for direct access
      publicId: result.id,
      fileSize: result.size,
      format: result.content_type,
      originalName: result.original_name,
      uploadedAt: result.uploaded_at,
      etag: result.etag,
      // Additional CDN-specific data
      apiUrl: result.url,
      cdnUrl: result.cdn_url
    });

    } catch (uploadError: any) {
      clearTimeout(timeoutId);

      if (uploadError?.name === 'AbortError') {
        const timeoutSeconds = timeoutDuration / 1000;
        console.error(`❌ Upload timeout after ${timeoutSeconds} seconds:`, {
          fileName: file.name,
          fileSize: file.size,
          uploadType,
          timeoutDuration
        });
        return NextResponse.json({
          error: `Upload timeout - file upload exceeded ${timeoutSeconds} second limit`,
          debug: {
            fileName: file.name,
            fileSize: file.size,
            timeout: `${timeoutSeconds} seconds`,
            suggestion: "Large files may need to be uploaded in smaller chunks or compressed. Consider reducing file size.",
            vercelLimit: "Vercel hobby plan limits function execution to 60 seconds"
          }
        }, { status: 408 });
      }

      throw uploadError; // Re-throw other errors
    }

  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json({ error: "Upload failed" }, { status: 500 });
  }
}
