import { NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get('email');
    if (!email) {
      return NextResponse.json({ error: 'Missing email' }, { status: 400 });
    }
    const user = await prisma.user.findUnique({ where: { email } });
    const premium = user?.headstealPremium ?? false;
    return NextResponse.json({ premium });
  } catch (err) {
    console.error('License check error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
