import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';
import { getFilePreview } from '@/lib/appwrite';

export async function GET(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all license requests for the user, ordered by most recent first
    const requests = await prisma.licenseRequest.findMany({
      where: { userId: session.user.id },
      orderBy: { requestedAt: 'desc' }
    });

    // Check cooldown status
    const twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000);
    const recentRequest = requests.find(req => req.requestedAt >= twelveHoursAgo);

    let cooldownInfo = null;
    if (recentRequest) {
      const nextAllowedTime = new Date(recentRequest.requestedAt.getTime() + 12 * 60 * 60 * 1000);
      const remainingTime = nextAllowedTime.getTime() - Date.now();

      if (remainingTime > 0) {
        cooldownInfo = {
          active: true,
          remainingTime: remainingTime,
          nextAllowedTime: nextAllowedTime.toISOString()
        };
      }
    }

    const formattedRequests = requests.map(request => ({
      ...request,
      screenshotUrl: getFilePreview(request.screenshotUrl)
    }));

    return NextResponse.json({
      requests: formattedRequests,
      cooldown: cooldownInfo || { active: false }
    });
  } catch (err) {
    console.error('License status error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
