import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';
import { getFilePreview } from '@/lib/appwrite';

export async function GET(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const requestData = await prisma.licenseRequest.findUnique({
      where: { userId: session.user.id }
    });

    if (!requestData) {
      return NextResponse.json({ request: null });
    }

    return NextResponse.json({
      request: {
        ...requestData,
        screenshotUrl: getFilePreview(requestData.screenshotUrl)
      }
    });
  } catch (err) {
    console.error('License status error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
