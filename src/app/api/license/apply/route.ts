import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';
import { uploadLicenseScreenshot } from '@/lib/appwrite';

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await req.formData();
    const spigotUsername = formData.get('spigotUsername') as string;
    const screenshot = formData.get('screenshot') as File;

    if (!spigotUsername || !screenshot) {
      return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
    }

    const existing = await prisma.licenseRequest.findUnique({
      where: { userId: session.user.id }
    });
    if (existing) {
      return NextResponse.json({ error: 'Request already submitted' }, { status: 400 });
    }

    const screenshotId = await uploadLicenseScreenshot(screenshot);

    const request = await prisma.licenseRequest.create({
      data: {
        userId: session.user.id,
        spigotUsername,
        screenshotUrl: screenshotId,
      }
    });

    return NextResponse.json({ request });
  } catch (err) {
    console.error('License apply error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
