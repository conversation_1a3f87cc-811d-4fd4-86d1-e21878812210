import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';
import { uploadLicenseScreenshot } from '@/lib/appwrite';

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await req.formData();
    const spigotUsername = formData.get('spigotUsername') as string;
    const screenshot = formData.get('screenshot') as File;

    if (!spigotUsername || !screenshot) {
      return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
    }

    // Check for cooldown period (12 hours)
    const twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000);
    const recentRequest = await prisma.licenseRequest.findFirst({
      where: {
        userId: session.user.id,
        requestedAt: {
          gte: twelveHoursAgo
        }
      },
      orderBy: {
        requestedAt: 'desc'
      }
    });

    if (recentRequest) {
      const nextAllowedTime = new Date(recentRequest.requestedAt.getTime() + 12 * 60 * 60 * 1000);
      const remainingTime = nextAllowedTime.getTime() - Date.now();

      if (remainingTime > 0) {
        const hours = Math.floor(remainingTime / (1000 * 60 * 60));
        const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));

        return NextResponse.json({
          error: 'Cooldown active',
          message: `You must wait ${hours}h ${minutes}m before submitting another application`,
          remainingTime: remainingTime,
          nextAllowedTime: nextAllowedTime.toISOString()
        }, { status: 429 });
      }
    }

    const screenshotId = await uploadLicenseScreenshot(screenshot);

    const request = await prisma.licenseRequest.create({
      data: {
        userId: session.user.id,
        spigotUsername,
        screenshotUrl: screenshotId,
      }
    });

    return NextResponse.json({ request });
  } catch (err) {
    console.error('License apply error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
