/*
 * Official Avehub Code, verified
 * Partnerships API Routes - Handles fetching all partnership applications
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextResponse } from "next/server";
import db from "@/lib/db";

const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

// [1] Define data models for type safety
interface PartnershipApp {
  id: string;
  userId: string; 
  username: string;
  companyName: string;
  websiteUrl: string;
  reason: string;
  experience: string;
  additional?: string | null;
  status: string;
  appliedAt: Date;
  statusUpdatedAt?: Date | null;
}

// [2] User model for email lookup
interface User {
  id: string;
  email: string | null;
}

// [3] GET - Retrieve all partnership applications
export async function GET(req: Request) {
  try {
    // [3.1] Security: Validate API key
    const apiKey = req.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      console.warn("API key validation failed", { 
        providedKey: apiKey?.slice(0, 3) + "..." || "none" 
      });
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // [3.2] Fetch all partnership applications from database
    console.log("Fetching partnership applications");
    const applications = await db.partnershipApplication.findMany({
      orderBy: {
        appliedAt: "desc"
      }
    });
    
    console.log(`Found ${applications.length} partnership applications`);

    if (applications.length === 0) {
      return NextResponse.json([]);
    }

    // [3.3] Get user data to associate emails with applications
    const users = await db.user.findMany({
      where: {
        id: {
          in: applications.map((app: PartnershipApp) => app.userId)
        }
      },
      select: {
        id: true,
        email: true
      }
    });
    
    console.log(`Found ${users.length} users associated with applications`);

    // [3.4] Create lookup map for efficient user retrieval
    const userMap = new Map<string, string | null>();
    users.forEach((user: User) => {
      userMap.set(user.id, user.email);
    });

    // [3.5] Format applications to include email information
    const formattedApplications = applications.map((app: PartnershipApp) => ({
      id: app.id,
      name: app.username,
      email: userMap.get(app.userId) || "",
      companyName: app.companyName,
      websiteUrl: app.websiteUrl,
      reason: app.reason,
      experience: app.experience,
      additional: app.additional,
      status: app.status,
      createdAt: app.appliedAt
    }));

    // [3.6] Return formatted applications
    return NextResponse.json(formattedApplications);
  } catch (error) {
    // [3.7] Handle and log any errors
    console.error("Error fetching partnership applications:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 