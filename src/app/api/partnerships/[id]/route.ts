/*
 * Official Avehub Code, verified
 * Partnership API Routes - Handles operations for individual partnership applications
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextResponse } from "next/server";
import { type NextRequest } from "next/server";
import db from "@/lib/db";

const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

// [1] Define data models for type safety
interface PartnershipApp {
  id: string;
  userId: string; 
  username: string;
  companyName: string;
  websiteUrl: string;
  reason: string;
  experience: string;
  additional?: string | null;
  status: string;
  appliedAt: Date;
  statusUpdatedAt?: Date | null;
}

// [2] User model for email lookup
interface User {
  id: string;
  email: string | null;
}

// [3] PATCH - Update status of a partnership application
export async function PATCH(request: NextRequest) {
  try {
    // [3.1] Extract ID from URL path parameters
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // [3.2] Security: Validate API key
    const apiKey = request.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }
    
    // [3.3] Extract and validate request data
    const body = await request.json();
    const { status } = body;

    // [3.4] Validate status is an allowed value
    if (status !== "approved" && status !== "rejected" && status !== "accepted") {
      return NextResponse.json(
        { error: "Invalid status value" },
        { status: 400 }
      );
    }

    // [3.5] Update partnership application in database
    const updatedApplication = await db.partnershipApplication.update({
      where: { id },
      data: { 
        status,
        statusUpdatedAt: new Date()
      }
    });

    // [3.6] Retrieve user email for the response
    const user = await db.user.findUnique({
      where: { id: updatedApplication.userId },
      select: { email: true }
    });

    // [3.7] Format response data
    const formattedApplication = {
      id: updatedApplication.id,
      name: updatedApplication.username,
      email: user?.email || "",
      companyName: updatedApplication.companyName,
      websiteUrl: updatedApplication.websiteUrl,
      reason: updatedApplication.reason,
      experience: updatedApplication.experience,
      additional: updatedApplication.additional,
      status: updatedApplication.status,
      createdAt: updatedApplication.appliedAt
    };

    return NextResponse.json(formattedApplication);
  } catch (error) {
    // [3.8] Handle and log any errors
    console.error("Error updating partnership status:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// [4] DELETE - Remove a partnership application
export async function DELETE(request: NextRequest) {
  try {
    // [4.1] Extract ID from URL path parameters
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    // [4.2] Security: Validate API key
    const apiKey = request.headers.get("x-api-key");
    if (apiKey !== API_KEY) {
      return NextResponse.json(
        { error: "Unauthorized: Invalid API Key." },
        { status: 403 }
      );
    }

    // [4.3] Verify the application exists before deletion
    const partnershipExists = await db.partnershipApplication.findUnique({
      where: { id }
    });

    if (!partnershipExists) {
      return NextResponse.json(
        { error: "Partnership application not found" },
        { status: 404 }
      );
    }

    // [4.4] Delete the application from database
    await db.partnershipApplication.delete({
      where: { id }
    });

    return NextResponse.json({ 
      success: true, 
      message: "Partnership application deleted successfully" 
    });
  } catch (error) {
    // [4.5] Handle and log any errors
    console.error("Error deleting partnership application:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 