// /workspaces/Avehub/src/app/announcement/page.tsx
import { redirect } from "next/navigation";
import prisma from "@/lib/db";

export default async function AnnouncementRedirectPage() {
  const announcement = await prisma.announcement.findFirst({
    orderBy: { createdAt: "desc" },
  });
  if (!announcement) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>No announcements available.</p>
      </div>
    );
  }
  redirect(`/announcement/${announcement.id}`);
}
