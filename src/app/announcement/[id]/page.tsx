"use client";
import { useFetchAnnouncements } from "@/hooks/announcement/useFetchAnnouncement";
import { Header } from "@/components/Header";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { Inter } from "next/font/google";
import LoadingDots from "@/components/animations/Loading";

const inter = Inter({ subsets: ["latin"], weight: ["400", "700"] });

export default function AnnouncementPage() {
  const { announcement, goToPrev, goToNext, currentIndex, announcements, loading, error } = useFetchAnnouncements();

  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  };

  if (loading) return (
    <div className="flex items-center justify-center min-h-screen">
      <LoadingDots />
    </div>
  );
  if (error) return <p className="text-red-500">{error}</p>;
  if (!announcement) return <p>No announcements found.</p>;

  return (
    <div>
      <Header />
      <div className={`${inter.className} min-h-screen flex flex-col items-center bg-gray-50 dark:bg-gray-900 p-8 space-y-8`}>
        <main className="w-full max-w-5xl py-16">
          <div className="text-left flex flex-col gap-8 mt-8">
            <motion.h1
              className="text-4xl font-extrabold text-gray-800 dark:text-white"
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
            >
              {announcement.title}
            </motion.h1>
            <motion.p
              className="text-lg text-gray-600 dark:text-gray-300"
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.2 }}
            >
              {announcement.description}
            </motion.p>
            <motion.p
              className="text-gray-500 dark:text-gray-400 italic"
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.4 }}
            >
              {announcement.footer}
            </motion.p>
            <motion.div
              className="flex justify-between"
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.6 }}
            >
              <motion.button
                onClick={goToPrev}
                disabled={currentIndex === announcements.length - 1}
                className="p-2 disabled:opacity-50"
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
              >
                <ArrowLeft className="w-6 h-6 text-gray-800 dark:text-white" />
              </motion.button>
              <motion.button
                onClick={goToNext}
                disabled={currentIndex === 0}
                className="p-2 disabled:opacity-50"
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
              >
                <ArrowRight className="w-6 h-6 text-gray-800 dark:text-white" />
              </motion.button>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
}
