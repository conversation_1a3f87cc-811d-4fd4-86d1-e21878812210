"use client";

import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";

export default function LoginPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-black px-4">
      <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl shadow-2xl p-8 max-w-md text-center">
        <h1 className="text-4xl font-bold text-white mb-4">Authentication Required</h1>
        <p className="text-gray-300 mb-6">
          You must be logged in to access this page. Please sign in using your Google account.
        </p>
        <button
          onClick={() => signIn("google", { callbackUrl: "/" })}
          className="bg-white text-black font-semibold py-2 px-6 rounded-full hover:bg-gray-200 transition-all duration-300"
        >
          Sign in with Google
        </button>

        <p
          onClick={() => router.push("/")}
          className="mt-4 text-gray-400 cursor-pointer hover:underline transition-all duration-200"
        >
          No Thanks
        </p>
      </div>
    </div>
  );
}
