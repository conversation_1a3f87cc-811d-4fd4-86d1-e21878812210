"use client";

import { motion } from "framer-motion";
import FallingConfetti from "../../../components/animations/effect/FallingBalls";

export default function AcceptedStatus({ application }: { application: any }) {
  return (
    <div className="min-h-screen overflow-hidden bg-gray-900 text-white relative flex items-center justify-center px-2">
      <FallingConfetti />
      <motion.div
        className="relative z-10 bg-zinc-900 border border-zinc-700 shadow-xl rounded-2xl p-10 w-full max-w-xl text-center mt-[-100px]"
        initial={{ y: 0 }}
        animate={{ y: [0, -20, 0] }}
        transition={{ duration: 4, ease: "easeInOut", repeat: Infinity }}
      >
        <h2 className="text-4xl font-semibold text-green-400 mb-4">
          ✅ Application Accepted
        </h2>
        <p className="text-zinc-300 text-lg">
          Congratulations! Your application has been accepted.
          <br />
          Welcome to the partnership program.
        </p>
      </motion.div>
    </div>
  );
}
