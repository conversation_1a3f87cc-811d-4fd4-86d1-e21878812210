"use client";
export default function RejectedStatus({ application }: { application: any }) {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8 bg-red-50 dark:bg-red-900">
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-8 max-w-2xl w-full text-center">
        <h2 className="text-3xl font-bold text-red-800 dark:text-red-300 mb-4">Application Rejected</h2>
        <p className="text-gray-600 dark:text-gray-300">
          Unfortunately, your application was not accepted. You may reapply after 30 days.
        </p>
      </div>
    </div>
  );
}
