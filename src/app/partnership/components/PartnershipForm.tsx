"use client";
import { useState } from "react";
import { useSession } from "next-auth/react";
import { motion } from "framer-motion";
import { usePartnershipApplication } from "@/hooks/partnership/usePartnershipApplication";

const inputClass =
  "w-full p-3 rounded border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 dark:text-gray-100";
const textareaClass = inputClass;

export default function PartnershipForm() {
  const { data: session } = useSession();
  const { applyForPartnership, loading, error, successMessage } = usePartnershipApplication();
  const [formData, setFormData] = useState({
    username: "",
    companyName: "",
    websiteUrl: "",
    reason: "",
    experience: "",
    additional: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!session?.user?.id) return;
    await applyForPartnership({ userId: session.user.id, ...formData });
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-8 bg-gray-100 dark:bg-gray-900">
      <motion.div
        className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-8 max-w-2xl w-full"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-3xl font-bold text-center text-gray-800 dark:text-white mb-6">
          Apply for Partnership
        </h2>
        {error && <p className="text-red-500 text-center mb-4">{error}</p>}
        {successMessage && <p className="text-green-500 text-center mb-4">{successMessage}</p>}
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <input type="text" name="username" placeholder="Username" onChange={handleChange} required className={inputClass} />
          <input type="text" name="companyName" placeholder="Company Name" onChange={handleChange} required className={inputClass} />
          <input type="url" name="websiteUrl" placeholder="Company Website" onChange={handleChange} required className={inputClass} />
          <textarea name="reason" placeholder="Why do you want to partner?" onChange={handleChange} required rows={3} className={textareaClass} />
          <textarea name="experience" placeholder="Your experience" onChange={handleChange} required rows={3} className={textareaClass} />
          <textarea name="additional" placeholder="Additional info (optional)" onChange={handleChange} rows={2} className={textareaClass} />
          <motion.button
            type="submit"
            disabled={loading}
            whileTap={{ scale: 0.95 }}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-lg hover:bg-blue-700 transition text-lg font-semibold"
          >
            {loading ? "Submitting..." : "Apply Now"}
          </motion.button>
        </form>
      </motion.div>
    </div>
  );
}
