"use client";
import { Header } from "@/components/Header";
import { motion } from "framer-motion";

export default function PrivacyPolicy() {
  return (
    <div>
      <Header />
      <div className="min-h-screen flex flex-col items-center justify-between bg-gray-50 dark:bg-gray-900 p-8">
        <main className="w-full max-w-5xl py-20">
          <div className="text-left flex flex-col gap-8">
            <motion.h1
              className="text-4xl sm:text-6xl font-extrabold text-gray-800 dark:text-white leading-tight"
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              Privacy <span className="bg-gradient-to-r from-blue-600 to-orange-500 bg-clip-text text-transparent">Policy</span>
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl text-gray-600 dark:text-gray-300"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              Your privacy is important to us at AveHub. This Privacy Policy outlines how we collect, use, and protect your information.
            </motion.p>

            <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated: 28/12/2024</p>

            {/** Section 1 */}
            <Section
              title="1. Information We Collect"
              description="We collect information to provide better services to our users. This includes:"
              listItems={[
                "Personal identification information (e.g., name, email address, phone number).",
                "Project-specific details provided by you.",
                "Payment information for billing purposes.",
              ]}
              delay={0.4}
            />

            {/** Section 2 */}
            <Section
              title="2. How We Use Your Information"
              description="Your information is used to:"
              listItems={[
                "Deliver the services you request.",
                "Improve our offerings and customer experience.",
                "Process payments and manage billing.",
              ]}
              delay={0.5}
            />

            {/** Section 3 */}
            <Section
              title="3. Sharing Your Information"
              description="We do not sell or share your personal information with third parties, except:"
              listItems={[
                "When required by law or legal processes.",
                "To trusted service providers who assist in delivering our services.",
              ]}
              delay={0.6}
            />

            {/** Section 4 */}
            <Section
              title="4. Data Security"
              description="We prioritize your data security and implement measures to protect it. However, no method of transmission over the Internet is 100% secure."
              delay={0.7}
            />

            {/** Section 5 */}
            <Section
              title="5. Your Rights"
              description="You have the right to:"
              listItems={[
                "Access the personal data we hold about you.",
                "Request corrections or deletions of your data.",
                "Opt out of data collection or processing.",
              ]}
              delay={0.8}
            />

            {/** Section 6 */}
            <Section
              title="6. Updates to This Policy"
              description="We may update this Privacy Policy from time to time. Changes will be posted on this page with an updated revision date."
              delay={0.9}
            />

            {/** Section 7 */}
            <Section
              title="7. Contact Us"
              description="If you have questions about this Privacy Policy, please contact us:"
              listItems={[
                "<strong>Email:</strong> <EMAIL>",
                "<strong>Discord:</strong> discord.avehubs.com",
              ]}
              isHtml
              delay={1}
            />

            <motion.p
              className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 mt-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.1 }}
            >
              By using AveHub&apos;s services, you consent to the practices described in this Privacy Policy.
            </motion.p>
          </div>
        </main>
      </div>
    </div>
  );
}

function Section({
  title,
  description,
  listItems,
  isHtml = false,
  delay,
}: {
  title: string;
  description: string;
  listItems?: string[];
  isHtml?: boolean;
  delay: number;
}) {
  return (
    <motion.section
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay }}
    >
      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">{title}</h2>
      {isHtml ? (
        <p
          className="text-gray-600 dark:text-gray-300"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      ) : (
        <p className="text-gray-600 dark:text-gray-300">{description}</p>
      )}
      {listItems && (
        <ul className="list-disc list-inside text-gray-600 dark:text-gray-300">
          {listItems.map((item, index) => (
            <li
              key={index}
              dangerouslySetInnerHTML={isHtml ? { __html: item } : undefined}
            >
              {!isHtml ? item : null}
            </li>
          ))}
        </ul>
      )}
    </motion.section>
  );
}