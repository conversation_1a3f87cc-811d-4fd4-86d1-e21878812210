"use client";
import { useSession } from "next-auth/react";
import StaffApplicationForm from "./components/StaffApplicationForm";
import PendingStatus from "./components/PendingStatus";
import ApprovedStatus from "./components/ApprovedStatus";
import RejectedStatus from "./components/RejectedStatus";
import { useStaffStatus } from "@/hooks/staff/useFetchStaffStatus";
import { Header } from "@/components/Header";
import LoadingDots from "@/components/animations/Loading";

export default function StaffApplicationPage() {
  const { data: session, status } = useSession();
  const { application, isLoading, error } = useStaffStatus(session?.user?.id);

  if (status === "loading" || isLoading) {
    return (
      <div>
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <LoadingDots />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-8 max-w-2xl w-full text-center">
            <h2 className="text-3xl font-bold text-red-500 mb-4">Error</h2>
            <p className="text-gray-600 dark:text-gray-300">
              {error}
            </p>
            <p className="mt-2 text-gray-500 dark:text-gray-400">
              Please try again later or contact support if the issue persists.
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  if (!application) {
    return (
      <div>
        <Header />
        <StaffApplicationForm />
      </div>
    );
  }
  
  // Add check for approved status as well
  switch (application.status) {
    case "pending":
      return (
        <div>
          <Header />
          <PendingStatus application={application} />
        </div>
      );
    case "accepted":
    case "approved": // Handle both 'accepted' and 'approved' status values
      return (
        <div>
          <Header />
          <ApprovedStatus application={application} />
        </div>
      );
    case "rejected":
      return (
        <div>
          <Header />
          <RejectedStatus application={application} />
        </div>
      );
    default:
      return (
        <div>
          <Header />
          <div className="min-h-screen flex items-center justify-center">
            <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-8 max-w-2xl w-full text-center">
              <h2 className="text-3xl font-bold text-amber-500 mb-4">Application Status</h2>
              <p className="text-gray-600 dark:text-gray-300">
                Current status: {application.status}
              </p>
              <p className="mt-2 text-gray-500 dark:text-gray-400">
                If you believe this is an error, please contact our HR <NAME_EMAIL>.
              </p>
            </div>
          </div>
        </div>
      );
  }
} 