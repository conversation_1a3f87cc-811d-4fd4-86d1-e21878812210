"use client";

import FallingConfetti from "@/components/animations/effect/FallingBalls";
import { CheckCircle } from "lucide-react";

interface AcceptedStatusProps {
  application: any;
}

export default function ApprovedStatus({ application }: AcceptedStatusProps) {
  // Format the application date
  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8 bg-gray-900 min-h-screen">
      <FallingConfetti />
      <div className="bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-white">Application Status</h1>
          <p className="mt-2 text-gray-400">
            Congratulations! Your application has been approved.
          </p>
        </div>

        <div className="space-y-8">
          <div className="bg-green-900/30 border border-green-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <div className="h-12 w-12 rounded-full bg-green-500 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-900" />
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-bold text-white">Application Approved</h2>
                <p className="text-green-300">Applied on {formatDate(application.appliedAt)}</p>
              </div>
            </div>
            <p className="text-gray-300">
              We're thrilled to welcome you to the AveHub team! Your application for the position of{" "}
              <span className="font-semibold text-green-200">{application.position}</span> has been approved.
            </p>
          </div>

          <div className="bg-gray-700/50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Next Steps</h3>
            <ol className="space-y-4 text-gray-300">
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">1</span>
                <span>Our HR team will reach out to you via email within the next 48 hours with your onboarding information.</span>
              </li>
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">2</span>
                <span>You'll receive an invitation to our staff Discord server and other collaboration tools.</span>
              </li>
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">3</span>
                <span>We'll schedule an orientation session to introduce you to the team and your responsibilities.</span>
              </li>
            </ol>
          </div>

          <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Have questions?</h3>
            <p className="text-gray-300 mb-4">
              If you have any questions about your role or the onboarding process, please contact our HR team at{" "}
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:underline">
                <EMAIL>
              </a>
            </p>
            <div className="mt-4">
              <a
                href="#"
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Join Staff Discord
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 