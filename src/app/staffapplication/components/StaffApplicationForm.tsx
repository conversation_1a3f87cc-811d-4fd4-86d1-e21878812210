"use client";

import { useState, FormEvent, ChangeEvent } from "react";
import { useSession } from "next-auth/react";
import { useStaffApplication } from "@/hooks/staff/useStaffApplication";
import { Loader2 } from "lucide-react";

const positions = [
  { value: "moderator", label: "Community Moderator" },
  { value: "developer", label: "Developer" },
  { value: "designer", label: "Designer" },
  { value: "support", label: "Support Staff" },
  { value: "other", label: "Other" }
];

const availabilityOptions = [
  { value: "full-time", label: "Full-time" },
  { value: "part-time", label: "Part-time" },
  { value: "weekends", label: "Weekends only" },
  { value: "flexible", label: "Flexible hours" }
];

export default function StaffApplicationForm() {
  const { data: session } = useSession();
  const { applyForStaffPosition, loading, error, successMessage } = useStaffApplication();

  const [formData, setFormData] = useState({
    fullName: "",
    age: "",
    location: "",
    availability: "full-time",
    position: "moderator",
    experience: "",
    skills: "",
    motivation: "",
    portfolioUrl: "",
    additionalInfo: ""
  });

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!session?.user?.id) {
      return;
    }

    // Basic validation
    if (!formData.fullName || !formData.age || !formData.location || 
        !formData.experience || !formData.skills || !formData.motivation) {
      alert("Please fill in all required fields.");
      return;
    }

    const age = parseInt(formData.age);
    if (isNaN(age) || age < 14 || age > 100) {
      alert("Please enter a valid age between 14 and 100.");
      return;
    }

    await applyForStaffPosition({
      userId: session.user.id,
      ...formData,
      age
    });
  };

  return (
    <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8 bg-gray-900 min-h-screen">
      <div className="bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-white">Staff Application</h1>
          <p className="mt-2 text-gray-400">
            Join our team and help us build the future of AveHub!
          </p>
        </div>

        {successMessage ? (
          <div className="bg-green-900/40 border border-green-500 text-green-200 p-6 rounded-lg text-center">
            <h3 className="text-xl font-bold mb-2">Application Submitted!</h3>
            <p>{successMessage}</p>
            <p className="mt-4">We'll review your application and get back to you soon.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-900/40 border border-red-500 text-red-200 p-4 rounded-lg">
                <p>{error}</p>
              </div>
            )}

            <div>
              <label htmlFor="fullName" className="block text-gray-300 font-medium mb-2">
                Full Name*
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                required
                value={formData.fullName}
                onChange={handleChange}
                className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="age" className="block text-gray-300 font-medium mb-2">
                  Age*
                </label>
                <input
                  type="number"
                  id="age"
                  name="age"
                  required
                  min="14"
                  max="100"
                  value={formData.age}
                  onChange={handleChange}
                  className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="location" className="block text-gray-300 font-medium mb-2">
                  Location*
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  required
                  placeholder="City, Country"
                  value={formData.location}
                  onChange={handleChange}
                  className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="position" className="block text-gray-300 font-medium mb-2">
                  Position*
                </label>
                <select
                  id="position"
                  name="position"
                  required
                  value={formData.position}
                  onChange={handleChange}
                  className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
                >
                  {positions.map(position => (
                    <option key={position.value} value={position.value}>
                      {position.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="availability" className="block text-gray-300 font-medium mb-2">
                  Availability*
                </label>
                <select
                  id="availability"
                  name="availability"
                  required
                  value={formData.availability}
                  onChange={handleChange}
                  className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
                >
                  {availabilityOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="experience" className="block text-gray-300 font-medium mb-2">
                Relevant Experience*
              </label>
              <textarea
                id="experience"
                name="experience"
                required
                rows={3}
                value={formData.experience}
                onChange={handleChange}
                placeholder="Describe your relevant work or volunteer experience"
                className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="skills" className="block text-gray-300 font-medium mb-2">
                Skills*
              </label>
              <textarea
                id="skills"
                name="skills"
                required
                rows={3}
                value={formData.skills}
                onChange={handleChange}
                placeholder="List your relevant skills (e.g., programming languages, design tools, moderation experience)"
                className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="motivation" className="block text-gray-300 font-medium mb-2">
                Motivation*
              </label>
              <textarea
                id="motivation"
                name="motivation"
                required
                rows={4}
                value={formData.motivation}
                onChange={handleChange}
                placeholder="Why do you want to join our team? What motivates you about this position?"
                className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="portfolioUrl" className="block text-gray-300 font-medium mb-2">
                Portfolio URL (Optional)
              </label>
              <input
                type="url"
                id="portfolioUrl"
                name="portfolioUrl"
                value={formData.portfolioUrl}
                onChange={handleChange}
                placeholder="https://yourportfolio.com"
                className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="additionalInfo" className="block text-gray-300 font-medium mb-2">
                Additional Information (Optional)
              </label>
              <textarea
                id="additionalInfo"
                name="additionalInfo"
                rows={3}
                value={formData.additionalInfo}
                onChange={handleChange}
                placeholder="Any other information you'd like to share with us"
                className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-white focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="text-right">
              <button
                type="submit"
                disabled={loading}
                className="inline-flex justify-center items-center py-3 px-6 border border-transparent shadow-sm text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-70"
              >
                {loading ? (
                  <>
                    <Loader2 className="animate-spin mr-2 h-5 w-5" />
                    Submitting...
                  </>
                ) : (
                  "Submit Application"
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
} 