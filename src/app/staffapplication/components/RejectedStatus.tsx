"use client";

import { XCircle } from "lucide-react";

interface RejectedStatusProps {
  application: any;
}

export default function RejectedStatus({ application }: RejectedStatusProps) {
  // Format the application date
  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  return (
    <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8 bg-gray-900 min-h-screen">
      <div className="bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-white">Application Status</h1>
          <p className="mt-2 text-gray-400">
            We've reviewed your application
          </p>
        </div>

        <div className="space-y-8">
          <div className="bg-red-900/30 border border-red-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <div className="h-12 w-12 rounded-full bg-red-500 flex items-center justify-center">
                <XCircle className="h-6 w-6 text-red-900" />
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-bold text-white">Application Not Selected</h2>
                <p className="text-red-300">Applied on {formatDate(application.appliedAt)}</p>
              </div>
            </div>
            <p className="text-gray-300">
              Thank you for your interest in the{" "}
              <span className="font-semibold text-red-200">{application.position}</span> position at AveHub. After careful consideration, we have decided to move forward with other candidates at this time.
            </p>
          </div>

          <div className="bg-gray-700/50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">What this means</h3>
            <p className="text-gray-300 mb-4">
              While we were impressed with your background and qualifications, we had many strong applicants for this position. This decision is not a reflection of your skills or potential.
            </p>
            <p className="text-gray-300">
              We encourage you to apply for future positions that match your skills and interests. Our team is growing, and new opportunities arise regularly.
            </p>
          </div>

          <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Future Opportunities</h3>
            <p className="text-gray-300 mb-4">
              If you'd like to receive notifications about future positions, you can sign up for our job alerts or check our careers page periodically.
            </p>
            <p className="text-gray-300 mb-4">
              If you have any questions, please contact our HR team at{" "}
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:underline">
                <EMAIL>
              </a>
            </p>
            <div className="mt-4">
              <a 
                href="#" 
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                View Other Positions
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 