"use client";
import { CheckCircle } from "lucide-react";

interface AcceptedStatusProps {
  application: {
    id: string;
    position: string;
    status: string;
    createdAt: string;
    appliedAt?: string | Date; // Support both fields since your API might use different ones
  };
}

export default function AcceptedStatus({ application }: AcceptedStatusProps) {
  // Format the application date
  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Use createdAt as fallback if appliedAt is not available
  const applicationDate = application.appliedAt || application.createdAt;

  return (
    <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8 bg-gray-900 min-h-screen">
      <div className="bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-white">Application Status</h1>
          <p className="mt-2 text-gray-400">
            Your application has been accepted for further review
          </p>
        </div>

        <div className="space-y-8">
          <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <div className="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-blue-900" />
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-bold text-white">Application Accepted</h2>
                <p className="text-blue-300">Applied on {formatDate(applicationDate)}</p>
              </div>
            </div>
            <p className="text-gray-300">
              Congratulations! Your application for the position of{" "}
              <span className="font-semibold text-blue-200">{application.position}</span> has been accepted for further review.
            </p>
          </div>

          <div className="bg-gray-700/50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Next Steps</h3>
            <ol className="space-y-4 text-gray-300">
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">1</span>
                <span>Our HR team is currently reviewing your application in detail.</span>
              </li>
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">2</span>
                <span>You will receive an email soon with information about a potential interview.</span>
              </li>
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">3</span>
                <span>Final decisions are typically made within 7-14 days.</span>
              </li>
            </ol>
          </div>

          <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Have questions?</h3>
            <p className="text-gray-300 mb-4">
              If you have any questions about the hiring process, please contact our HR team at{" "}
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 