"use client";

import { Clock } from "lucide-react";

interface PendingStatusProps {
  application: any;
}

export default function PendingStatus({ application }: PendingStatusProps) {
  // Format the application date
  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  return (
    <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8 bg-gray-900 min-h-screen">
      <div className="bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-white">Application Status</h1>
          <p className="mt-2 text-gray-400">
            Your staff application is currently being reviewed
          </p>
        </div>

        <div className="space-y-8">
          <div className="bg-yellow-900/30 border border-yellow-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <div className="h-12 w-12 rounded-full bg-yellow-500 flex items-center justify-center">
                <Clock className="h-6 w-6 text-yellow-900" />
              </div>
              <div className="ml-4">
                <h2 className="text-xl font-bold text-white">Application In Review</h2>
                <p className="text-yellow-300">Submitted on {formatDate(application.appliedAt)}</p>
              </div>
            </div>
            <p className="text-gray-300">
              Thank you for applying to join our team! Your application for the position of{" "}
              <span className="font-semibold text-yellow-200">{application.position}</span> is currently under review by our team.
            </p>
          </div>

          <div className="bg-gray-700/50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">What happens next?</h3>
            <ol className="space-y-4 text-gray-300">
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">1</span>
                <span>Our team reviews all applications carefully (this typically takes 1-2 weeks).</span>
              </li>
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">2</span>
                <span>If your application is selected, we'll contact you via email to schedule an interview.</span>
              </li>
              <li className="flex">
                <span className="bg-gray-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 flex-shrink-0 text-sm">3</span>
                <span>Based on the interview, we'll make a final decision and notify you of the outcome.</span>
              </li>
            </ol>
          </div>

          <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Need to update your application?</h3>
            <p className="text-gray-300 mb-4">
              If you need to update your application or have any questions, please contact our HR team at{" "}
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 