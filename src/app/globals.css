@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  scroll-behavior: smooth;
}

::-webkit-scrollbar {
  width: 10px; 
  height: 10px; 
}

::-webkit-scrollbar-track {
  background: transparent; 
  border-radius: 5px; 
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #ff8c00, #0a74da); 
  border-radius: 5px; 
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #cf5f0a, #0d64b6); 
}

::-webkit-scrollbar-corner {
  background: #0a74da; 
}

.nav-link {
  color: #7b8aa5; 
  background-color: transparent; 
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: transparent;
}

.nav-link.active {
  color: transparent;
  background: linear-gradient(90deg, #2563eb, #f97316);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-color: transparent; 
}


.project-box {
  margin-top: 80px;
}

html {
  scroll-behavior: smooth;
}


@keyframes bubble {
  0% {
    transform: translateY(0);
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100vh);
    opacity: 0;
  }
}
