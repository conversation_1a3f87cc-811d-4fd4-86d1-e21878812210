'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Play, RotateCcw, Ghost, WavesLadder } from 'lucide-react';
import Link from 'next/link';

type Bubble = {
  id: number;
  size: number;
  left: number;
  duration: number;
  opacity: number;
  drift: number;
};

type PoppingBubble = {
  id: number;
  size: number;
  x: number;
  y: number;
};

export default function NotFound() {
  const [isClient, setIsClient] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [score, setScore] = useState(0);
  const [cursorPos, setCursorPos] = useState({ x: 0, y: 0 });

  const [bgBubbles, setBgBubbles] = useState<Bubble[]>([]);
  const [bubbles, setBubbles] = useState<Bubble[]>([]);
  const [poppingBubbles, setPoppingBubbles] = useState<PoppingBubble[]>([]);

  const nextIdRef = useRef(0);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const popSoundsRef = useRef<HTMLAudioElement[]>([]);

  useEffect(() => {
    setIsClient(true);
    if (typeof Audio !== 'undefined' && popSoundsRef.current.length === 0) {
      const files = ['pop1.mp3', 'pop2.mp3', 'pop3.mp3'];
      popSoundsRef.current = files.map((f) => {
        const audio = new Audio(`/sounds/${f}`);
        audio.volume = 0.5;
        audio.load();
        return audio;
      });
    }
  }, []);

  const createBubble = useCallback((id: number): Bubble => ({
    id,
    size: 10 + Math.random() * 30,
    left: Math.random() * 100,
    duration: 4 + Math.random() * 6,
    opacity: 0.3 + Math.random() * 0.4,
    drift: (Math.random() - 0.5) * 20,
  }), []);

  useEffect(() => {
    if (!isClient) return;
    const initialBg: Bubble[] = [];
    for (let i = 0; i < 8; i++) initialBg.push(createBubble(nextIdRef.current++));
    setBgBubbles(initialBg);
  }, [isClient, createBubble]);

  const handleBubbleReachTop = useCallback(
    (id: number) => {
      if (!gameStarted || gameOver) return;
      setGameOver(true);
      setGameStarted(false);
      setBubbles([]);
    },
    [gameStarted, gameOver]
  );

  const playPopSound = () => {
    if (popSoundsRef.current.length) {
      const idx = Math.floor(Math.random() * popSoundsRef.current.length);
      const snd = popSoundsRef.current[idx];
      snd.currentTime = 0;
      snd.play().catch(() => {});
    }
  };

  const popBubble = useCallback(
    (bubble: Bubble, rect: DOMRect) => {
      playPopSound();
      setScore((s) => s + 1);
      setBubbles((prev) => prev.filter((b) => b.id !== bubble.id));
      const containerRect = containerRef.current?.getBoundingClientRect();
      if (containerRect) {
        setPoppingBubbles((prev) => [
          ...prev,
          {
            id: bubble.id,
            size: bubble.size,
            x: rect.left - containerRect.left + rect.width / 2,
            y: rect.top - containerRect.top + rect.height / 2,
          },
        ]);
      }
    }, []);

  const handleGlobalClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!gameStarted || gameOver) return;
    const container = containerRef.current;
    if (!container) return;
    const radius = 30;
    const elements = container.querySelectorAll<HTMLElement>('.game-bubble');
    elements.forEach((el) => {
      const rect = el.getBoundingClientRect();
      const dx = e.clientX - (rect.left + rect.width / 2);
      const dy = e.clientY - (rect.top + rect.height / 2);
      if (Math.hypot(dx, dy) <= radius) {
        const id = Number(el.dataset.id);
        const bubble = bubbles.find((b) => b.id === id);
        if (bubble) popBubble(bubble, rect);
      }
    });
  };

  useEffect(() => {
    if (!isClient || !gameStarted || gameOver) return;
    if (bubbles.length >= 7) return;
    const timeout = setTimeout(() => {
      const count = Math.min(1 + Math.floor(Math.random() * 3), 7 - bubbles.length);
      if (count > 0) {
        setBubbles((prev) => [...prev, ...Array.from({ length: count }, () => createBubble(nextIdRef.current++))]);
      }
    }, 800 + Math.random() * 800);
    return () => clearTimeout(timeout);
  }, [bubbles, isClient, gameStarted, gameOver, createBubble]);

  const startGame = useCallback(() => {
    setGameStarted(true);
    setGameOver(false);
    setScore(0);
    setBgBubbles([]);
    setBubbles(Array.from({ length: 5 }, () => createBubble(nextIdRef.current++)));
  }, [createBubble]);

  return (
    <div
      ref={containerRef}
      onMouseMove={(e) => {
        const r = containerRef.current?.getBoundingClientRect();
        if (r) setCursorPos({ x: e.clientX - r.left, y: e.clientY - r.top });
      }}
      onClick={handleGlobalClick}
      className="relative h-screen w-full bg-blue-950 overflow-hidden flex items-center justify-center text-white cursor-none"
    >
      {/* Background glow */}
      <div className="absolute top-[-100px] left-1/2 -translate-x-1/2 h-[500px] w-[500px] rounded-full bg-blue-500 blur-[150px] opacity-50 pointer-events-none" />

      {/* Cursor circle always visible */}
      <div
        className="absolute z-50 pointer-events-none rounded-full border border-white bg-white/10"
        style={{ width: 60, height: 60, left: cursorPos.x - 30, top: cursorPos.y - 30 }}
      />

      {/* Start button */}
      {!gameStarted && !gameOver && (
        <button onClick={startGame} className="absolute top-4 right-4 z-20 inline-flex items-center gap-2 bg-blue-700 hover:bg-blue-600 px-5 py-2 rounded-full shadow-md">
          <Play className="w-4 h-4" /> Start Game
        </button>
      )}

      {/* Background 404 bubbles */}
      {isClient && !gameStarted && !gameOver && bgBubbles.map((b) => (
        <motion.div
          key={b.id}
          className="absolute rounded-full bg-blue-300"
          style={{ width: b.size, height: b.size, left: `${b.left}%`, bottom: -b.size, opacity: b.opacity }}
          initial={{ y: 0, x: b.drift }}
          animate={{ y: '-110vh', x: b.drift }}
          transition={{ duration: b.duration, ease: 'linear', repeat: Infinity, repeatDelay: 0.5 + Math.random() * 0.5 }}
        />
      ))}

      {/* 404 message */}
      {!gameStarted && !gameOver && (
        <motion.div initial={{ y: -10 }} animate={{ y: 10 }} transition={{ duration: 3, repeat: Infinity, repeatType: 'reverse', ease: 'easeInOut' }} className="z-10 text-center pointer-events-none select-none">
          <div className="text-7xl font-bold tracking-wider text-blue-200 drop-shadow-lg">404</div>
          <div className="mt-2 flex items-center gap-2 text-lg text-blue-300">
            <Ghost className="w-5 h-5" /> Page sunk deep in the ocean...
          </div>
          <div className="mt-6 pointer-events-auto">
            <Link href="/" className="inline-flex items-center gap-2 bg-blue-700 hover:bg-blue-600 px-6 py-3 rounded-full text-lg font-semibold shadow-lg">
              <WavesLadder className="w-5 h-5" /> Swim Back Home
            </Link>
          </div>
        </motion.div>
      )}

      {/* Score */}
      {gameStarted && !gameOver && (
        <div className="absolute top-4 right-4 z-10 rounded-full bg-blue-800 px-4 py-2 text-lg shadow-lg">Score: {score}</div>
      )}

      {/* Game bubbles */}
      {isClient && gameStarted && !gameOver && bubbles.map((b) => (
        <motion.div
          key={b.id}
          data-id={b.id}
          className="absolute game-bubble rounded-full bg-blue-400 shadow-md"
          style={{ width: b.size, height: b.size, left: `${b.left}%`, bottom: -b.size, opacity: b.opacity }}
          initial={{ y: 0, x: b.drift }}
          animate={{ y: '-110vh', x: b.drift }}
          transition={{ duration: b.duration, ease: 'linear' }}
          onAnimationComplete={() => handleBubbleReachTop(b.id)}
        />
      ))}

      {/* Pop effects */}
      <AnimatePresence>
        {poppingBubbles.map((pop) => (
          <motion.div
            key={`pop-${pop.id}-${pop.x}-${pop.y}`}
            className="absolute rounded-full bg-blue-200 border border-white"
            style={{ width: pop.size, height: pop.size, left: pop.x - pop.size/2, top: pop.y - pop.size/2 }}
            initial={{ scale: 1, opacity: 1 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
            exit={{ opacity: 0 }}
            onAnimationComplete={() => setPoppingBubbles((prev) => prev.filter((p) => p.id !== pop.id))}
          />
        ))}
      </AnimatePresence>

      {/* Game over screen */}
      {gameOver && (
        <div className="z-10 text-center">
          <div className="mb-4 text-5xl font-bold">Game Over</div>
          <div className="mb-6 text-2xl">Score: {score}</div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button onClick={() => { setGameOver(false); startGame(); }} className="inline-flex items-center gap-2 bg-blue-700 hover:bg-blue-600 px-6 py-3 rounded-full text-lg font-semibold shadow-lg">
              <RotateCcw className="w-5 h-5" /> Restart
            </button>
            <button onClick={() => window.location.reload()} className="inline-flex items-center gap-2 bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-full text-lg font-semibold shadow-lg">
              <ArrowLeft className="w-5 h-5" /> Back
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
