'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { 
  ArrowLeft,
  Download,
  Calendar,
  RefreshCw,
  User,
  MessageSquare,
  Send,
  Trash2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';
import { Header } from '@/components/Header';

// TypeScript interfaces
interface Comment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  createdAt: string;
}

interface Plugin {
  id: string;
  name: string;
  description: string;
  publisherId: string;
  publisherName: string;
  version: string;
  releaseType: string;
  releaseDate: string;
  updateDate: string;
  fileLocation: string;
  downloads: number;
  comments: Comment[];
}

// Simple loading component
const PluginSkeleton = () => (
  <div className="animate-pulse">
    <div className="h-10 w-1/3 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
    <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
    <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
  </div>
);

export default function PluginDetailPage() {
  const params = useParams();
  const id = params.id as string;
  
  const router = useRouter();
  const { data: session } = useSession();
  
  const [plugin, setPlugin] = useState<Plugin | null>(null);
  const [loading, setLoading] = useState(true);
  const [commentText, setCommentText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  // Simplified date formatting
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Simplified fetch with no caching or timeout
  const fetchPlugin = async () => {
    try {
      setLoading(true);
      
      // Simple direct fetch with no-store to prevent caching
      const response = await fetch(`/api/plugins/${id}`, {
        cache: 'no-store'
      });
      
      if (response.ok) {
        const data = await response.json();
        setPlugin(data);
      } else {
        toast.error("Failed to load plugin information");
        router.push('/plugins');
      }
    } catch (error) {
      console.error('Error fetching plugin:', error);
      toast.error("Failed to load plugin information");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlugin();
  }, [id]);

  // Updated download function that uses the server-side download route
  const handleDownload = async () => {
    if (!plugin) {
      toast.error("Plugin information not available");
      return;
    }
    
    try {
      setIsDownloading(true);
      
      // Get the filename from the plugin name and version
      const fileName = `${plugin.name.replace(/[^a-zA-Z0-9]/g, '-')}-v${plugin.version}.zip`;
      
      // Use the download API route
      window.location.href = `/api/plugins/${plugin.id}/download`;
      
      toast.success("Download started", { 
        description: `${fileName} is being downloaded to your downloads folder`
      });
      
      // Short timeout to allow the toast to show before download starts
      setTimeout(() => {
        setIsDownloading(false);  
      }, 2000);
      
    } catch (error) {
      console.error('Error initiating download:', error);
      toast.error("Failed to download plugin");
      setIsDownloading(false);
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session) {
      toast.error("Please sign in to leave a comment");
      return;
    }
    
    if (!commentText.trim()) {
      toast.error("Please enter a comment");
      return;
    }
    
    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/plugins/${id}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: commentText }),
      });
      
      if (response.ok) {
        setCommentText('');
        fetchPlugin(); // Refresh comments
        toast.success("Comment added successfully");
      } else {
        throw new Error("Failed to add comment");
      }
    } catch (error) {
      toast.error("Failed to add comment");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!session) return;
    
    try {
      const response = await fetch(`/api/plugins/${id}/comments`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ commentId }),
      });
      
      if (response.ok) {
        fetchPlugin(); // Refresh comments
        toast.success("Comment deleted successfully");
      } else {
        throw new Error("Failed to delete comment");
      }
    } catch (error) {
      toast.error("Failed to delete comment");
    }
  };

  // Basic loading state
  if (loading) {
    return (
      <>
        <Header />
        <div className="container mx-auto py-8 px-4 md:px-6">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/plugins')}
            className="mb-6"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Plugins
          </Button>
          
          <PluginSkeleton />
        </div>
      </>
    );
  }

  if (!plugin) {
    return (
      <>
        <Header />
        <div className="container mx-auto py-8 px-4 text-center">
          <h1 className="text-2xl font-bold mb-4">Plugin not found</h1>
          <Button onClick={() => router.push('/plugins')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Plugins
          </Button>
        </div>
      </>
    );
  }

  return (
    <>
      <Header />
      <div className="container mx-auto py-8 px-4 md:px-6">
        <Button 
          variant="ghost" 
          onClick={() => router.push('/plugins')}
          className="mb-6"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Plugins
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Plugin Details */}
          <div className="col-span-2">
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h1 className="text-3xl font-bold">{plugin.name}</h1>
                  <div className="flex items-center text-muted-foreground mt-2">
                    <User className="h-4 w-4 mr-1" />
                    <span>by {plugin.publisherName}</span>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Badge className="text-lg py-1 px-3">v{plugin.version}</Badge>
                  {plugin.releaseType === 'snapshot' && (
                    <Badge variant="secondary" className="py-1 px-3">
                      Snapshot
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mb-6">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>Released: {formatDate(plugin.releaseDate)}</span>
                </div>
                <div className="flex items-center">
                  <RefreshCw className="h-4 w-4 mr-1" />
                  <span>Updated: {formatDate(plugin.updateDate)}</span>
                </div>
                <div className="flex items-center">
                  <Download className="h-4 w-4 mr-1" />
                  <span>{plugin.downloads} Downloads</span>
                </div>
              </div>

              <Separator className="my-6" />

              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Description</h2>
                <p className="whitespace-pre-line">{plugin.description}</p>
              </div>

              <Button 
                className="w-full mt-8" 
                size="lg" 
                onClick={handleDownload}
                disabled={isDownloading}
              >
                {isDownloading ? (
                  <>
                    <span className="animate-spin mr-2 h-5 w-5 border-t-2 border-b-2 border-current rounded-full" />
                    Downloading...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-5 w-5" />
                    Download Plugin
                  </>
                )}
              </Button>
            </div>

            {/* Comments Section */}
            <div className="bg-card rounded-lg p-6 shadow-sm mt-8">
              <h2 className="text-xl font-semibold mb-6">
                Comments ({plugin.comments?.length || 0})
              </h2>

              {session && (
                <form onSubmit={handleSubmitComment} className="mb-8">
                  <Textarea
                    placeholder="Leave a comment..."
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    className="min-h-[100px] mb-2"
                  />
                  <Button 
                    type="submit" 
                    disabled={isSubmitting || !commentText.trim()}
                    className="ml-auto flex"
                  >
                    <Send className="mr-2 h-4 w-4" />
                    {isSubmitting ? 'Posting...' : 'Post Comment'}
                  </Button>
                </form>
              )}

              {!session && (
                <div className="bg-muted p-4 rounded-md mb-6 text-center">
                  <p>Please sign in to leave a comment</p>
                </div>
              )}

              {plugin.comments && plugin.comments.length > 0 ? (
                <div className="space-y-6">
                  {plugin.comments.map((comment) => (
                    <Card key={comment.id}>
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-center">
                          <div className="font-semibold">{comment.userName}</div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(comment.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p>{comment.content}</p>
                      </CardContent>
                      {(session?.user?.id === comment.userId || session?.user?.admin) && (
                        <CardFooter className="pt-0 flex justify-end">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDeleteComment(comment.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </CardFooter>
                      )}
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-20" />
                  <p>No comments yet. Be the first to comment!</p>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-span-1">
            <div className="bg-card rounded-lg p-6 shadow-sm sticky top-6">
              <h2 className="text-xl font-semibold mb-4">About the Publisher</h2>
              <div className="flex items-center space-x-3 mb-6">
                <div className="bg-muted rounded-full h-12 w-12 flex items-center justify-center">
                  <User className="h-6 w-6" />
                </div>
                <div>
                  <div className="font-medium">{plugin.publisherName}</div>
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <h3 className="font-medium mb-2">Plugin Information</h3>
              <dl className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <dt className="text-muted-foreground">Version:</dt>
                  <dd>{plugin.version}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-muted-foreground">Release Type:</dt>
                  <dd>{plugin.releaseType === 'snapshot' ? 'Snapshot' : 'Release'}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-muted-foreground">Released:</dt>
                  <dd>{formatDate(plugin.releaseDate)}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-muted-foreground">Last updated:</dt>
                  <dd>{formatDate(plugin.updateDate)}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-muted-foreground">Downloads:</dt>
                  <dd>{plugin.downloads}</dd>
                </div>
              </dl>
              
              <Button 
                className="w-full mt-6" 
                size="lg" 
                onClick={handleDownload}
                disabled={isDownloading}
              >
                {isDownloading ? (
                  <>
                    <span className="animate-spin mr-2 h-5 w-5 border-t-2 border-b-2 border-current rounded-full" />
                    Downloading...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-5 w-5" />
                    Download
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 