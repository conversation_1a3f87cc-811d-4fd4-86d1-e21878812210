'use client';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/Header';
import { useLicenseStatus } from '@/hooks/license/useLicenseStatus';
import LoadingDots from '@/components/animations/Loading';
import ApplicationHistory from './components/ApplicationHistory';
import CooldownDisplay from './components/CooldownDisplay';
import EnhancedLicenseForm from './components/EnhancedLicenseForm';
import { CheckCircle, AlertCircle, Info } from 'lucide-react';

export default function LicensePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { requests, cooldown, loading, error, refetch } = useLicenseStatus();

  // Redirect to login if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <LoadingDots />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <div className="bg-red-900/20 border border-red-600 rounded-lg p-6 text-center">
              <AlertCircle className="mx-auto text-red-500 mb-4" size={48} />
              <h2 className="text-xl font-semibold text-red-400 mb-2">Error Loading License Data</h2>
              <p className="text-gray-300 mb-4">{error}</p>
              <button
                onClick={refetch}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const hasApprovedRequest = requests.some(req => req.status === 'approved');
  const hasPendingRequest = requests.some(req => req.status === 'pending');

  return (
    <div className="min-h-screen bg-gray-900">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Headsteal License Management</h1>
            <p className="text-gray-400">
              Manage your Headsteal Premium license applications and view their status
            </p>
          </div>

          {/* Premium Status Banner */}
          {hasApprovedRequest && (
            <div className="bg-green-900/20 border border-green-600 rounded-lg p-6 mb-6">
              <div className="flex items-center gap-3">
                <CheckCircle className="text-green-500" size={24} />
                <div>
                  <h3 className="text-green-400 font-semibold">Premium License Active</h3>
                  <p className="text-gray-300 text-sm">
                    Your Headsteal Premium license has been approved and is active.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Pending Application Notice */}
          {hasPendingRequest && !hasApprovedRequest && (
            <div className="bg-blue-900/20 border border-blue-600 rounded-lg p-6 mb-6">
              <div className="flex items-center gap-3">
                <Info className="text-blue-500" size={24} />
                <div>
                  <h3 className="text-blue-400 font-semibold">Application Under Review</h3>
                  <p className="text-gray-300 text-sm">
                    Your license application is being reviewed by our team. You'll be notified once it's processed.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Cooldown Display */}
          {cooldown.active && cooldown.remainingTime && cooldown.nextAllowedTime && (
            <CooldownDisplay
              remainingTime={cooldown.remainingTime}
              nextAllowedTime={cooldown.nextAllowedTime}
            />
          )}

          <div className="grid gap-6 lg:grid-cols-2">
            {/* Application History */}
            <div className="lg:col-span-2">
              <ApplicationHistory requests={requests} />
            </div>

            {/* Application Form */}
            <div className="lg:col-span-2">
              <EnhancedLicenseForm
                onSuccess={refetch}
                disabled={cooldown.active}
              />
            </div>
          </div>

          {/* Help Section */}
          <div className="mt-8 bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-3">Need Help?</h3>
            <div className="text-gray-300 text-sm space-y-2">
              <p>• Make sure your Spigot username matches exactly with your SpigotMC.org account</p>
              <p>• Upload a clear screenshot showing your Headsteal purchase from the SpigotMC website</p>
              <p>• You can submit a new application every 12 hours if your previous one was rejected</p>
              <p>• Contact support if you're having issues with your application</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
