'use client';
import { useSession } from 'next-auth/react';
import { Header } from '@/components/Header';
import LicenseForm from './components/LicenseForm';
import { useLicenseStatus } from '@/hooks/license/useLicenseStatus';
import PendingStatus from './components/PendingStatus';
import ApprovedStatus from './components/ApprovedStatus';
import RejectedStatus from './components/RejectedStatus';
import LoadingDots from '@/components/animations/Loading';

export default function LicensePage() {
  const { status } = useSession();
  const { request, loading } = useLicenseStatus();

  if (status === 'loading' || loading) {
    return (
      <div>
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <LoadingDots />
        </div>
      </div>
    );
  }

  if (!request) {
    return (
      <div>
        <Header />
        <LicenseForm />
      </div>
    );
  }

  switch (request.status) {
    case 'approved':
      return (
        <div>
          <Header />
          <ApprovedStatus />
        </div>
      );
    case 'rejected':
      return (
        <div>
          <Header />
          <RejectedStatus />
        </div>
      );
    default:
      return (
        <div>
          <Header />
          <PendingStatus />
        </div>
      );
  }
}
