'use client';
import { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

interface CooldownDisplayProps {
  remainingTime: number;
  nextAllowedTime: string;
}

export default function CooldownDisplay({ remainingTime: initialRemainingTime, nextAllowedTime }: CooldownDisplayProps) {
  const [remainingTime, setRemainingTime] = useState(initialRemainingTime);

  useEffect(() => {
    const interval = setInterval(() => {
      const nextTime = new Date(nextAllowedTime).getTime();
      const now = Date.now();
      const remaining = nextTime - now;
      
      if (remaining <= 0) {
        setRemainingTime(0);
        clearInterval(interval);
      } else {
        setRemainingTime(remaining);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [nextAllowedTime]);

  const formatTime = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  if (remainingTime <= 0) {
    return null;
  }

  return (
    <div className="bg-yellow-900/20 border border-yellow-600 rounded-lg p-4 mb-6">
      <div className="flex items-center gap-3">
        <Clock className="text-yellow-500" size={24} />
        <div>
          <h3 className="text-yellow-400 font-semibold">Application Cooldown Active</h3>
          <p className="text-gray-300 text-sm mt-1">
            You can submit another license application in: <span className="font-mono text-yellow-400">{formatTime(remainingTime)}</span>
          </p>
          <p className="text-gray-400 text-xs mt-1">
            Next application allowed at: {new Date(nextAllowedTime).toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  );
}
