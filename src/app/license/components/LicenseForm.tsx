'use client';
import { useState, ChangeEvent, FormEvent } from 'react';
import { useLicenseApplication } from '@/hooks/license/useLicenseApplication';

export default function LicenseForm() {
  const { apply, loading, error } = useLicenseApplication();
  const [spigotUsername, setSpigotUsername] = useState('');
  const [screenshot, setScreenshot] = useState<File | null>(null);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!screenshot) return;
    await apply({ spigotUsername, screenshot });
  };

  return (
    <div className="max-w-xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-4">Headsteal License Request</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && <p className="text-red-500">{error}</p>}
        <div>
          <label className="block mb-1">Spigot Username</label>
          <input className="w-full p-2 bg-gray-700 rounded" value={spigotUsername} onChange={e => setSpigotUsername(e.target.value)} required />
        </div>
        <div>
          <label className="block mb-1">Purchase Screenshot</label>
          <input type="file" accept="image/*" onChange={(e: ChangeEvent<HTMLInputElement>) => e.target.files && setScreenshot(e.target.files[0])} required />
        </div>
        <button disabled={loading} className="bg-blue-600 px-4 py-2 rounded text-white">
          {loading ? 'Submitting...' : 'Submit'}
        </button>
      </form>
    </div>
  );
}
