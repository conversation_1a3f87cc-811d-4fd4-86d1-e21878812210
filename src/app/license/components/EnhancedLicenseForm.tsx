'use client';
import { useState, ChangeEvent, FormEvent } from 'react';
import { useLicenseApplication } from '@/hooks/license/useLicenseApplication';
import { Upload, AlertCircle, CheckCircle } from 'lucide-react';

interface EnhancedLicenseFormProps {
  onSuccess?: () => void;
  disabled?: boolean;
}

export default function EnhancedLicenseForm({ onSuccess, disabled = false }: EnhancedLicenseFormProps) {
  const { apply, loading, error, cooldownError } = useLicenseApplication();
  const [spigotUsername, setSpigotUsername] = useState('');
  const [screenshot, setScreenshot] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!screenshot || disabled) return;
    
    const result = await apply({ spigotUsername, screenshot });
    if (result) {
      setSuccess(true);
      setSpigotUsername('');
      setScreenshot(null);
      setTimeout(() => setSuccess(false), 3000);
      onSuccess?.();
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        setScreenshot(file);
      }
    }
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setScreenshot(e.target.files[0]);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <h3 className="text-xl font-semibold text-white mb-4">Submit New License Application</h3>
      
      {success && (
        <div className="mb-4 p-4 bg-green-900/20 border border-green-600 rounded-lg flex items-center gap-3">
          <CheckCircle className="text-green-500" size={20} />
          <p className="text-green-400">Application submitted successfully!</p>
        </div>
      )}

      {cooldownError && (
        <div className="mb-4 p-4 bg-yellow-900/20 border border-yellow-600 rounded-lg flex items-start gap-3">
          <AlertCircle className="text-yellow-500 mt-0.5" size={20} />
          <div>
            <p className="text-yellow-400 font-medium">Cooldown Active</p>
            <p className="text-gray-300 text-sm mt-1">{cooldownError.message}</p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-4 bg-red-900/20 border border-red-600 rounded-lg flex items-center gap-3">
            <AlertCircle className="text-red-500" size={20} />
            <p className="text-red-400">{error}</p>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Spigot Username <span className="text-red-400">*</span>
          </label>
          <input 
            type="text"
            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
            placeholder="Enter your Spigot username"
            value={spigotUsername} 
            onChange={e => setSpigotUsername(e.target.value)} 
            required 
            disabled={disabled || loading}
          />
          <p className="text-gray-400 text-xs mt-1">
            This should match your username on SpigotMC.org
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Purchase Screenshot <span className="text-red-400">*</span>
          </label>
          <div
            className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive 
                ? 'border-blue-500 bg-blue-500/10' 
                : 'border-gray-600 hover:border-gray-500'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => !disabled && document.getElementById('screenshot-input')?.click()}
          >
            <input
              id="screenshot-input"
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
              required
              disabled={disabled || loading}
            />
            
            <Upload className="mx-auto text-gray-400 mb-2" size={32} />
            
            {screenshot ? (
              <div>
                <p className="text-green-400 font-medium">{screenshot.name}</p>
                <p className="text-gray-400 text-sm">Click to change file</p>
              </div>
            ) : (
              <div>
                <p className="text-gray-300">Drop your screenshot here or click to browse</p>
                <p className="text-gray-400 text-sm mt-1">PNG, JPG, GIF up to 10MB</p>
              </div>
            )}
          </div>
          <p className="text-gray-400 text-xs mt-1">
            Upload a screenshot showing your Headsteal purchase from SpigotMC
          </p>
        </div>

        <button 
          type="submit"
          disabled={disabled || loading || !screenshot || !spigotUsername.trim()}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          {loading ? 'Submitting Application...' : 'Submit Application'}
        </button>
      </form>
    </div>
  );
}
