'use client';
import { LicenseRequest } from '@/hooks/license/useLicenseStatus';
import { CheckCircle, XCircle, Clock, ExternalLink } from 'lucide-react';

interface ApplicationHistoryProps {
  requests: LicenseRequest[];
}

export default function ApplicationHistory({ requests }: ApplicationHistoryProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="text-green-500" size={20} />;
      case 'rejected':
        return <XCircle className="text-red-500" size={20} />;
      default:
        return <Clock className="text-yellow-500" size={20} />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Approved</span>;
      case 'rejected':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Rejected</span>;
      default:
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (requests.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 text-center">
        <p className="text-gray-400">No license applications found</p>
        <p className="text-gray-500 text-sm mt-1">Submit your first application below</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <h3 className="text-xl font-semibold text-white mb-4">Application History</h3>
      <div className="space-y-4">
        {requests.map((request, index) => (
          <div key={request.id} className="border border-gray-700 rounded-lg p-4 hover:border-gray-600 transition-colors">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                {getStatusIcon(request.status)}
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-white font-medium">Application #{requests.length - index}</span>
                    {getStatusBadge(request.status)}
                  </div>
                  <p className="text-gray-300 text-sm">
                    <span className="font-medium">Spigot Username:</span> {request.spigotUsername}
                  </p>
                  <p className="text-gray-400 text-xs mt-1">
                    Submitted: {formatDate(request.requestedAt)}
                  </p>
                  {request.updatedAt !== request.requestedAt && (
                    <p className="text-gray-400 text-xs">
                      Updated: {formatDate(request.updatedAt)}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <a
                  href={request.screenshotUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <ExternalLink size={14} />
                  View Screenshot
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
