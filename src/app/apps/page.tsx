/*
 * Official Avehub Code, verified
 * Public Apps Store - Browse and discover applications
 * Any unauthorized modifications will invalidate service warranty
 */

"use client";

import { useState, useEffect } from "react";
import { Header } from "@/components/Header";
import Footer from "@/components/Footer";
import { motion } from "framer-motion";
import {
  Search,
  Grid3X3,
  List
} from "lucide-react";
import { toast } from "sonner";
import {
  AppListing,
  APP_CATEGORIES,
  SORT_OPTIONS,
  ViewMode,
  AppFilters
} from "@/types/app";
import { AppCard } from "@/components/apps/AppCard";
import { LoadingState } from "@/components/apps/LoadingState";
import { ErrorState } from "@/components/apps/ErrorState";

export default function AppsStore() {
  const [apps, setApps] = useState<AppListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");

  // [3] Fetch apps
  useEffect(() => {
    fetchApps();
  }, [searchQuery, selectedCategory, sortBy, sortOrder]);

  const fetchApps = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: AppFilters = {
        status: "APPROVED",
        search: searchQuery,
        sortBy,
        order: sortOrder
      };

      if (selectedCategory !== "All") {
        filters.category = selectedCategory;
      }

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== "") {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/apps?${params}`);
      if (response.ok) {
        const data = await response.json();
        setApps(data);
      } else {
        throw new Error(`Failed to fetch apps: ${response.status}`);
      }
    } catch (error) {
      console.error("Error fetching apps:", error);
      setError("Failed to load apps. Please try again.");
      toast.error("Failed to load apps");
    } finally {
      setLoading(false);
    }
  };

  // [4] Retry function
  const handleRetry = () => {
    fetchApps();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* [6] Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            App Store
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Discover amazing applications built by our developer community
          </p>
        </motion.div>

        {/* [7] Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            {/* Search Bar */}
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search apps..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Categories */}
              <div className="flex flex-wrap gap-2">
                {APP_CATEGORIES.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>

              {/* Sort and View Options */}
              <div className="flex items-center space-x-4">
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-') as [string, "asc" | "desc"];
                    setSortBy(field);
                    setSortOrder(order);
                  }}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  {SORT_OPTIONS.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 ${
                      viewMode === "grid"
                        ? "bg-blue-600 text-white"
                        : "bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    }`}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 ${
                      viewMode === "list"
                        ? "bg-blue-600 text-white"
                        : "bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* [8] Apps Grid/List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {loading ? (
            <LoadingState type={viewMode === "list" ? "list" : "grid"} />
          ) : error ? (
            <ErrorState
              type="error"
              title="Failed to load apps"
              message={error}
              onRetry={handleRetry}
            />
          ) : apps.length === 0 ? (
            <ErrorState
              type="empty"
              title="No apps found"
              message="Try adjusting your search or filter criteria"
            />
          ) : (
            <div className={viewMode === "grid"
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
            }>
              {apps.map((app) => (
                <AppCard
                  key={app.id}
                  app={app}
                  viewMode={viewMode}
                />
              ))}
            </div>
          )}
        </motion.div>
      </div>

      <Footer />
    </div>
  );
}
