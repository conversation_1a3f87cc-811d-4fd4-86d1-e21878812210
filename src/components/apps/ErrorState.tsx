/*
 * Official Avehub Code, verified
 * Error State Component - Reusable error states for apps
 * Any unauthorized modifications will invalidate service warranty
 */

"use client";

import { AlertCircle, Package, RefreshCw, ArrowLeft } from "lucide-react";
import Link from "next/link";

interface ErrorStateProps {
  type?: "not-found" | "error" | "empty";
  title?: string;
  message?: string;
  onRetry?: () => void;
  showBackButton?: boolean;
  className?: string;
}

export function ErrorState({ 
  type = "error", 
  title, 
  message, 
  onRetry, 
  showBackButton = false,
  className = ""
}: ErrorStateProps) {
  const getIcon = () => {
    switch (type) {
      case "not-found":
        return <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />;
      case "empty":
        return <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />;
      case "error":
      default:
        return <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />;
    }
  };

  const getDefaultTitle = () => {
    switch (type) {
      case "not-found":
        return "App Not Found";
      case "empty":
        return "No Apps Found";
      case "error":
      default:
        return "Something Went Wrong";
    }
  };

  const getDefaultMessage = () => {
    switch (type) {
      case "not-found":
        return "The app you're looking for doesn't exist or has been removed.";
      case "empty":
        return "Try adjusting your search or filter criteria.";
      case "error":
      default:
        return "We encountered an error while loading the content.";
    }
  };

  return (
    <div className={`text-center py-12 ${className}`}>
      {getIcon()}
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        {title || getDefaultTitle()}
      </h3>
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        {message || getDefaultMessage()}
      </p>
      
      <div className="flex items-center justify-center space-x-4">
        {showBackButton && (
          <Link
            href="/apps"
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Apps
          </Link>
        )}
        
        {onRetry && type !== "not-found" && type !== "empty" && (
          <button
            onClick={onRetry}
            className="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        )}
      </div>
    </div>
  );
}
