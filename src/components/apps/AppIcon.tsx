/*
 * Official Avehub Code, verified
 * App Icon Component - Reusable app icon with fallback handling
 * Any unauthorized modifications will invalidate service warranty
 */

"use client";

import { Package } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface AppIconProps {
  iconUrl?: string;
  appName: string;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
}

const sizeClasses = {
  sm: "w-8 h-8",
  md: "w-16 h-16",
  lg: "w-24 h-24",
  xl: "w-32 h-32"
};

const iconSizes = {
  sm: { width: 32, height: 32 },
  md: { width: 64, height: 64 },
  lg: { width: 96, height: 96 },
  xl: { width: 128, height: 128 }
};

const fallbackIconSizes = {
  sm: "w-4 h-4",
  md: "w-8 h-8",
  lg: "w-12 h-12",
  xl: "w-16 h-16"
};

export function AppIcon({ iconUrl, appName, size = "md", className = "" }: AppIconProps) {
  const [imageError, setImageError] = useState(false);

  // Get icon URL with proper handling for api.avehubs.com only
  const getIconUrl = (iconUrl?: string) => {
    if (!iconUrl) return null;

    // If it's already a full URL and from api.avehubs.com, use it
    if (iconUrl.startsWith('https://api.avehubs.com')) {
      return iconUrl;
    }

    // If it's already a full URL but not from api.avehubs.com, return null
    if (iconUrl.startsWith('http')) {
      return null;
    }

    // For relative paths or file IDs, we can't construct the URL without more context
    // Return null to show fallback icon
    return null;
  };

  const iconSrc = getIconUrl(iconUrl);
  const sizeClass = sizeClasses[size];
  const { width, height } = iconSizes[size];
  const fallbackIconSize = fallbackIconSizes[size];

  // Show fallback if no valid icon URL or if image failed to load
  const showFallback = !iconSrc || imageError;

  return (
    <div className={`relative ${sizeClass} ${className}`}>
      {iconSrc && !imageError && (
        <Image
          src={iconSrc}
          alt={appName}
          width={width}
          height={height}
          className={`${sizeClass} rounded-lg object-cover`}
          onError={() => setImageError(true)}
          priority={size === "xl"}
        />
      )}
      {showFallback && (
        <div className={`${sizeClass} bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center`}>
          <Package className={`${fallbackIconSize} text-gray-500`} />
        </div>
      )}
    </div>
  );
}
