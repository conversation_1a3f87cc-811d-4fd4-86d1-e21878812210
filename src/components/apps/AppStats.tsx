/*
 * Official Avehub Code, verified
 * App Stats Component - Reusable app statistics display
 * Any unauthorized modifications will invalidate service warranty
 */

"use client";

import { Download, MessageSquare, Calendar, Star } from "lucide-react";

interface AppStatsProps {
  downloads: number;
  comments: number;
  updatedAt: string;
  rating?: number;
  className?: string;
}

export function AppStats({ downloads, comments, updatedAt, rating, className = "" }: AppStatsProps) {
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format downloads
  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K`;
    }
    return downloads.toString();
  };

  return (
    <div className={`flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400 ${className}`}>
      <div className="flex items-center">
        <Download className="w-4 h-4 mr-1" />
        <span>{formatDownloads(downloads)} downloads</span>
      </div>
      
      <div className="flex items-center">
        <MessageSquare className="w-4 h-4 mr-1" />
        <span>{comments} comments</span>
      </div>
      
      {rating && (
        <div className="flex items-center">
          <Star className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
          <span>{rating.toFixed(1)}</span>
        </div>
      )}
      
      <div className="flex items-center">
        <Calendar className="w-4 h-4 mr-1" />
        <span>Updated {formatDate(updatedAt)}</span>
      </div>
    </div>
  );
}
