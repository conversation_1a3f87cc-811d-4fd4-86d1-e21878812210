/*
 * Official Avehub Code, verified
 * App Card Component - Reusable app card for listings
 * Any unauthorized modifications will invalidate service warranty
 */

"use client";

import { Download, MessageSquare, Calendar } from "lucide-react";
import Link from "next/link";
import { AppListing, ViewMode } from "@/types/app";
import { AppIcon } from "./AppIcon";

interface AppCardProps {
  app: AppListing;
  viewMode: ViewMode;
}

export function AppCard({ app, viewMode }: AppCardProps) {
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format downloads
  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K`;
    }
    return downloads.toString();
  };

  return (
    <Link
      href={`/apps/${app.id}`}
      className="block bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
    >
      <div className={viewMode === "grid" ? "space-y-4" : "flex items-start space-x-4"}>
        {/* App Icon and Info */}
        <div className={viewMode === "grid" ? "flex items-start space-x-4" : ""}>
          <AppIcon
            iconUrl={app.iconUrl}
            appName={app.name}
            size="md"
          />

          <div className={viewMode === "grid" ? "flex-1 min-w-0" : "flex-1 min-w-0"}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {app.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              by {app.developer.name}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {app.shortDescription || app.description}
            </p>
          </div>
        </div>

        {/* App Stats */}
        <div className={viewMode === "grid" ? "flex items-center justify-between text-sm text-gray-500 dark:text-gray-400" : "flex flex-col items-end text-sm text-gray-500 dark:text-gray-400"}>
          <div className={viewMode === "grid" ? "flex items-center space-x-4" : "space-y-1 text-right"}>
            <span className="flex items-center">
              <Download className="w-4 h-4 mr-1" />
              {formatDownloads(app.downloads)}
            </span>
            <span className="flex items-center">
              <MessageSquare className="w-4 h-4 mr-1" />
              {app._count.comments}
            </span>
            <span className="flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              {formatDate(app.createdAt)}
            </span>
          </div>

          <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
            {app.category}
          </span>
        </div>
      </div>
    </Link>
  );
}
