"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

type Confetti = {
  id: number;
  x: number;
  size: number;
  initialRotation: number;
  color: string;
};

export default function FallingConfetti() {
  const [confettiPieces, setConfettiPieces] = useState<Confetti[]>([]);
  const [screenHeight, setScreenHeight] = useState<number>(0);

  useEffect(() => {
    setScreenHeight(window.innerHeight);
  }, []);

  const colors = ['#FFC700', '#FF0000', '#2E3192', '#41BBC7', '#7FDBB6'];

  useEffect(() => {
    const addConfetti = () => {
      const newConfetti: Confetti = {
        id: Date.now() + Math.random(),
        x: Math.random() * window.innerWidth,
        size: Math.random() * 5 + 10,
        initialRotation: Math.random() * 360,
        color: colors[Math.floor(Math.random() * colors.length)],
      };

      setConfettiPieces((prev) => [...prev, newConfetti]);

      setTimeout(() => {
        setConfettiPieces((prev) =>
          prev.filter((conf) => conf.id !== newConfetti.id)
        );
      }, 4000);
    };

    const interval = setInterval(addConfetti, 200);
    return () => clearInterval(interval);
  }, []);

  return (
    <div
      className="fixed top-0 left-0 w-screen h-screen pointer-events-none z-[9999]"
      style={{ overflow: "hidden" }}
    >
      {confettiPieces.map((conf) => (
        <motion.div
          key={conf.id}
          className="absolute"
          style={{
            left: `${conf.x}px`,
            top: "0px",
            width: `${conf.size}px`,
            height: `${conf.size * 0.4}px`,
            backgroundColor: conf.color,
            borderRadius: "2px",
          }}
          initial={{ y: 0, opacity: 1, rotate: conf.initialRotation }}
          animate={{
            y: screenHeight,
            opacity: 0,
            rotate: conf.initialRotation + 90,
          }}
          transition={{ duration: 4, ease: "easeInOut" }}
        />
      ))}
    </div>
  );
}
