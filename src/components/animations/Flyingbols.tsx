import { motion } from 'framer-motion';

const getRandomColor = () => {
  const colors = [
    'bg-blue-500',
    'bg-red-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-purple-500',
    'bg-teal-500',
    'bg-pink-500',
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

const Flyingbols = () => {
  const balls = new Array(7).fill(null);
  return (
    <div className="absolute inset-0 w-full h-full pointer-events-none overflow-hidden z-0">
      {balls.map((_, index) => (
        <motion.div
          key={index}
          className={`absolute w-10 h-10 ${getRandomColor()} rounded-full`}
          animate={{
            x: [0, 150 + index * 20, 0, -(150 + index * 20), 0],
            y: [0, -(150 + index * 20), 0, 150 + index * 20, 0],
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: Math.random() * 3,
          }}
          style={{
            left: `${Math.random() * 90}%`,
            top: `${Math.random() * 90}%`,
          }}
        />
      ))}
    </div>
  );
};

export default Flyingbols;
