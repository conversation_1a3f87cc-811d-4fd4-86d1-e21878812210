import { motion } from "framer-motion";

const LoadingDots = () => {
  const colors = ["bg-blue-500", "bg-orange-500", "bg-blue-500"]; 

  return (
      <div className="flex space-x-2">
        {colors.map((color, i) => (
          <motion.span
            key={i}
            className={`w-4 h-4 rounded-full ${color}`}
            animate={{ y: [0, -10, 0] }}
            transition={{
              repeat: Infinity,
              duration: 0.6,
              ease: "easeInOut",
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
  );
};

export default LoadingDots;
