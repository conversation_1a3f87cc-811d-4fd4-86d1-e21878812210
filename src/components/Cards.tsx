import { motion } from "framer-motion";
import { projectCards } from "@/utils/cards";
import Flyingbols from "./animations/Flyingbols";
import Image from "next/image";

interface CardProps {
  title: string;
  description: string;
  imageUrl: string;
  link: string;
}

const Card = ({ title, description, imageUrl, link }: CardProps) => {
  return (
    <motion.div
      className="relative text-gray-100 rounded-xl shadow-md p-4 min-w-[250px] max-w-[250px] flex flex-col justify-between z-10 backdrop-blur-md"
      initial={{ scale: 0.95, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
    >
      <div className="relative">
        <div className="absolute inset-0 w-full h-full rounded-xl bg-transprant blur-lg opacity-10 pointer-events-none"></div>
        <Image
          src={imageUrl}
          alt={title}
          width={64}
          height={64}
          className="relative rounded-full object-cover mx-auto"
        />
      </div>
      <h2 className="text-center text-lg font-bold mt-4 text-gray-100">{title}</h2>
      <p className="text-center text-sm mt-2 text-gray-400">{description}</p>
      <a
        href={link}
        className="text-center bg-gray-700 hover:bg-gray-600 transition text-gray-100 rounded-lg py-2 mt-auto"
      >
        View Project
      </a>
    </motion.div>
  );
};

export const OurProjects = () => {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-transprant px-4">
      <div className="relative bg-transprant p-8 rounded-2xl shadow-2xl w-full max-w-6xl z-10 overflow-hidden">
        <Flyingbols />
        <h2 className="text-3xl font-bold text-gray-100 mb-6 text-center relative z-10">
          Our Projects
        </h2>
        <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-thin scrollbar-thumb-gray-800 scrollbar-track-gray-700">
          {projectCards.map((card, index) => (
            <Card
              key={index}
              title={card.title}
              description={card.description}
              imageUrl={card.imageUrl}
              link={card.link}
            />
          ))}
        </div>

        {/* Soft glow effect in the corner */}
        <div className="absolute w-[200px] h-[200px] bg-gradient-to-br from-gray-800 to-gray-700 blur-[120px] opacity-40 rounded-full -bottom-10 -right-10 z-0" />
      </div>
    </div>
  );
};
