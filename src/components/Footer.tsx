/*
 * Official Avehub Code, verified
 * Footer Component - Main footer component for the application
 * Any unauthorized modifications will invalidate service warranty
 */

import React from "react";
import Link from "next/link";
import { Github, Mail, Briefcase, Users, Heart, Code, Zap } from "lucide-react";
import { config, socal } from "@/utils/config";
import { motion } from "framer-motion";

// Discord SVG icon component
const DiscordIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="currentColor"
    className="text-gray-400 group-hover:text-white transition"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M20.317 4.369A19.791 19.791 0 0 0 15.84 3c-.2.36-.428.832-.585 1.201a18.223 18.223 0 0 0-5.528 0 12.64 12.64 0 0 0-.597-1.201 19.736 19.736 0 0 0-4.482 1.37C2.16 9.139 1.3 13.692 1.65 18.184a19.99 19.99 0 0 0 5.82 2.854 14.77 14.77 0 0 0 1.25-2.007 12.98 12.98 0 0 1-2.027-.967c.17-.127.337-.263.496-.4a14.37 14.37 0 0 0 9.616 0c.16.14.327.277.498.404a12.47 12.47 0 0 1-2.03.96c.36.7.79 1.39 1.27 2.03a19.926 19.926 0 0 0 5.816-2.854c.473-5.83-.785-10.03-2.513-13.816zM8.02 15.5c-1.066 0-1.93-.985-1.93-2.201 0-1.215.846-2.2 1.93-2.2 1.09 0 1.954.996 1.93 2.21 0 1.215-.846 2.19-1.93 2.19zm7.94 0c-1.066 0-1.93-.985-1.93-2.201 0-1.215.846-2.2 1.93-2.2 1.09 0 1.954.996 1.93 2.21 0 1.215-.846 2.19-1.93 2.19z" />
  </svg>
);

const Footer = () => {
  return (
    <footer className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-16 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500"></div>

      <motion.div
        className="absolute top-10 left-10 w-20 h-20 bg-purple-500/10 rounded-full blur-xl"
        animate={{ y: [0, -10, 0], x: [0, 5, 0] }}
        transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
      />
      <motion.div
        className="absolute bottom-10 right-10 w-16 h-16 bg-blue-500/10 rounded-full blur-xl"
        animate={{ y: [0, 10, 0], x: [0, -5, 0] }}
        transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
      />

      <div className="relative z-10 max-w-7xl mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12 mb-12">
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
                <Code size={24} className="text-white" />
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                About AveHub
              </h3>
            </div>
            <p className="text-gray-300 leading-relaxed mb-6">
              We're a passionate team of developers creating innovative projects and sharing knowledge with the global community.
              Our mission is to build tools that empower developers and make technology more accessible to everyone.
            </p>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <Heart size={16} className="text-red-400" />
                <span>Made with love</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap size={16} className="text-yellow-400" />
                <span>Powered by innovation</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="text-center lg:text-left"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold mb-6 text-gray-100">Connect With Us</h3>
            <div className="space-y-4">
              <Link
                href={socal.routes.GitHub}
                className="group flex items-center gap-3 text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2"
                target="_blank"
              >
                <div className="p-2 bg-gray-800 group-hover:bg-gray-700 rounded-lg transition-colors">
                  <Github size={18} />
                </div>
                <span className="font-medium">GitHub</span>
              </Link>
              <Link
                href={socal.routes.discord}
                className="group flex items-center gap-3 text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2"
                target="_blank"
              >
                <div className="p-2 bg-gray-800 group-hover:bg-gray-700 rounded-lg transition-colors">
                  <DiscordIcon />
                </div>
                <span className="font-medium">Discord</span>
              </Link>
              <Link
                href="mailto:<EMAIL>"
                className="group flex items-center gap-3 text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2"
              >
                <div className="p-2 bg-gray-800 group-hover:bg-gray-700 rounded-lg transition-colors">
                  <Mail size={18} />
                </div>
                <span className="font-medium">Email Us</span>
              </Link>
            </div>
          </motion.div>

          <motion.div
            className="text-center lg:text-left"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold mb-6 text-gray-100">Get Involved</h3>
            <p className="text-gray-400 mb-6 text-sm leading-relaxed">
              Ready to make an impact? Join our community and help us build the future of development tools.
            </p>
            <div className="space-y-3">
              <Link
                href={config.routes.partnership}
                className="group inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg w-full justify-center lg:justify-start"
              >
                <Briefcase size={18} />
                <span className="font-medium">Partnership</span>
              </Link>
              <Link
                href={config.routes.staffapplication}
                className="group inline-flex items-center gap-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg w-full justify-center lg:justify-start"
              >
                <Users size={18} />
                <span className="font-medium">Join Team</span>
              </Link>
            </div>
          </motion.div>
        </div>

        <motion.div
          className="border-t border-gray-700/50 pt-8"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-gray-400">
              © {new Date().getFullYear()} AveHub. All rights reserved.
            </div>
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <Link href="/tos" className="hover:text-white transition-colors">Terms</Link>
              <Link href="/pp" className="hover:text-white transition-colors">Privacy</Link>
              <span className="flex items-center gap-2">
                <span>Built with</span>
                <Heart size={14} className="text-red-400" />
                <span>by AveHub Team</span>
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
