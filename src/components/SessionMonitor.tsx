"use client";

import { useSession } from "next-auth/react";
import { useEffect, useRef, useState } from "react";
import { getUserPermissions } from "@/utils/sessionUtils";

interface SessionMonitorProps {
  onSessionChange?: (permissions: any) => void;
  children?: React.ReactNode;
}

export default function SessionMonitor({ onSessionChange, children }: SessionMonitorProps) {
  const { data: session, update } = useSession();
  const lastPermissionsRef = useRef<any>(null);

  useEffect(() => {
    if (!session) return;

    const currentPermissions = getUserPermissions(session);
    const lastPermissions = lastPermissionsRef.current;

    const permissionsChanged =
      lastPermissions &&
      (
        lastPermissions.isDeveloper !== currentPermissions.isDeveloper ||
        lastPermissions.isAdmin !== currentPermissions.isAdmin
      );

    if (permissionsChanged) {
      console.log("Session permissions changed:", {
        old: lastPermissions,
        new: currentPermissions
      });

      // Notify parent component
      if (onSessionChange) {
        onSessionChange(currentPermissions);
      }

      // Trigger session update (optional and safe now)
      void update();
    }

    // Store current permissions without causing re-render
    lastPermissionsRef.current = currentPermissions;
  }, [session, onSessionChange, update]);

  return <>{children}</>;
}

/**
 * Hook for components that need to monitor session changes
 */
export function useSessionMonitor() {
  const { data: session, status, update } = useSession();
  const [permissions, setPermissions] = useState<any>(null);

  useEffect(() => {
    if (session) {
      const currentPermissions = getUserPermissions(session);
      setPermissions(currentPermissions);
    }
  }, [session]);

  const forceRefresh = async () => {
    await update();
  };

  return {
    session,
    status,
    permissions,
    forceRefresh,
    isLoading: status === "loading"
  };
}
