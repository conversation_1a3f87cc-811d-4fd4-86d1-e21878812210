"use client";

import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import { useState } from "react";

// App Store FAQ Data
const appStoreFAQs = [
  {
    question: "What is AveHub?",
    answer: "AveHub is a platform where we specialize in creating custom websites, Discord bots, and more. We offer tailored solutions to fit your specific needs."
  },
  {
    question: "How can I order a custom project?",
    answer: "To order a custom project, simply click the 'Order Now' button. We'll get in touch with you to discuss your project idea, and we'll provide a price estimate after understanding your requirements."
  },
  {
    question: "How do I know the price of my custom project?",
    answer: "Once you share your idea with us, we will evaluate the scope of the project and give you a price estimate based on its complexity and requirements."
  },
  {
    question: "Can I request any type of project?",
    answer: "Yes! You can request any custom project, and we’ll work with you to make it happen. Whether it’s a website, Discord bot, or something else, just let us know your ideas."
  },
  {
    question: "How long will it take to complete my custom project?",
    answer: "The timeline for each project varies depending on its complexity. After discussing your project with us, we’ll provide an estimated completion time."
  },
  {
    question: "What is the Ave App Store?",
    answer: "The Ave App Store is your gateway to discovering amazing applications built by the AveHub team. From productivity tools to entertainment apps, we've got everything you need."
  },
  {
    question: "Are the apps free to download?",
    answer: "Most of our apps are completely free! Some premium features may require a subscription, but we always offer generous free tiers."
  },
  {
    question: "Can I use these apps on mobile and desktop?",
    answer: "Yes! Our apps are designed to work seamlessly across all your devices - mobile, tablet, and desktop."
  },
  {
    question: "How often are apps updated?",
    answer: "We regularly update our apps with new features, bug fixes, and security improvements. Updates are automatic and hassle-free."
  },
  {
    question: "Is my data safe with Ave apps?",
    answer: "Absolutely! We prioritize your privacy and security. All data is encrypted and we follow industry-best security practices."
  },
  {
    question: "Can I request new features?",
    answer: "We love hearing from our users! You can submit feature requests through our Discord community or contact forms."
  },
  {
    question: "Do you offer customer support?",
    answer: "Yes! We provide 24/7 customer support through multiple channels including email, chat, and our community forums."
  },
  {
    question: "Can I develop apps for the Ave App Store?",
    answer: "We're always looking for talented developers! Contact us through our partnership program to learn about development opportunities."
  }
];

export default function AppStoreFAQ() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  return (
    <section className="py-20 px-8">
      <div className="max-w-4xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Frequently Asked Questions
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400">
            Everything you need to know about the Ave App Store
          </p>
        </motion.div>

        <div className="space-y-4">
          {appStoreFAQs.map((faq, index) => (
            <motion.div
              key={index}
              className="bg-white/20 dark:bg-gray-800/20 backdrop-blur-sm rounded-2xl border border-purple-200/30 dark:border-purple-700/30 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <button
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-purple-50/50 dark:hover:bg-purple-900/20 transition-colors duration-200"
                onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
              >
                <span className="font-semibold text-gray-800 dark:text-white">{faq.question}</span>
                <motion.div
                  animate={{ rotate: openFAQ === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronDown className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </motion.div>
              </button>
              <motion.div
                initial={false}
                animate={{
                  height: openFAQ === index ? "auto" : 0,
                  opacity: openFAQ === index ? 1 : 0
                }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="px-6 pb-4 text-gray-600 dark:text-gray-400">
                  {faq.answer}
                </div>
              </motion.div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
