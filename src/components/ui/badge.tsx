import React from "react";
import clsx from "clsx";

interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: "default" | "secondary" | "outline" | "destructive";
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, children, variant = "default", ...props }, ref) => {
    const variantClasses: Record<string, string> = {
      default: "bg-blue-600 text-white hover:bg-blue-700",
      secondary: "bg-gray-200 text-gray-800 hover:bg-gray-300",
      outline: "border border-gray-300 text-gray-700 bg-white hover:bg-gray-100",
      destructive: "bg-red-600 text-white hover:bg-red-700",
    };

    return (
      <div
        ref={ref}
        className={clsx(
          "inline-flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium transition-colors duration-200",
          variantClasses[variant],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Badge.displayName = "Badge";

export { Badge, type BadgeProps };
