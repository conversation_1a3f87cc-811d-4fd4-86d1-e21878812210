"use client";

import React from "react";

interface AlertDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

const AlertDialog: React.FC<AlertDialogProps> = ({ 
  open = false, 
  onOpenChange, 
  children 
}) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
      <div className="fixed inset-0 z-50 bg-black/50" onClick={() => onOpenChange && onOpenChange(false)}></div>
      <div className="z-50 bg-gray-800 rounded-lg border border-gray-700 shadow-lg w-full max-w-md">
        {children}
      </div>
    </div>
  );
};

const AlertDialogContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={`relative p-6 ${className || ""}`}>
      {children}
    </div>
  );
};

const AlertDialogHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={`flex flex-col space-y-1.5 pb-3 ${className || ""}`}>
      {children}
    </div>
  );
};

const AlertDialogTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <h2 className={`text-xl font-semibold leading-none tracking-tight ${className || ""}`}>
      {children}
    </h2>
  );
};

const AlertDialogDescription: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <p className={`text-sm text-gray-400 ${className || ""}`}>
      {children}
    </p>
  );
};

const AlertDialogFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ${className || ""}`}>
      {children}
    </div>
  );
};

const AlertDialogAction: React.FC<{
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}> = ({ children, className, onClick, disabled = false }) => {
  return (
    <button
      className={`inline-flex items-center justify-center px-4 py-2 font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${className || ""}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

const AlertDialogCancel: React.FC<{
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}> = ({ children, className, onClick }) => {
  return (
    <button
      className={`inline-flex items-center justify-center px-4 py-2 font-medium rounded-md border border-gray-700 bg-transparent text-gray-300 hover:bg-gray-700 ${className || ""}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
};

export {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel
}; 