"use client";

import React, { createContext, useContext, useState, useEffect } from "react";

type TabsContextValue = {
  activeTab: string;
  setActiveTab: (id: string) => void;
};

const TabsContext = createContext<TabsContextValue | undefined>(undefined);

interface TabsProps {
  defaultValue?: string;
  value?: string;
  onValueChange?: (id: string) => void;
  className?: string;
  children: React.ReactNode;
  onChange?: (id: string) => void;
}

const Tabs: React.FC<TabsProps> = ({ 
  defaultValue, 
  value,
  onValueChange,
  className, 
  children, 
  onChange 
}) => {
  const [activeTab, setActiveTab] = useState(value || defaultValue || "");

  // Update internal state when controlled value changes
  useEffect(() => {
    if (value !== undefined) {
      setActiveTab(value);
    }
  }, [value]);

  const handleTabChange = (id: string) => {
    if (value === undefined) {
      // Only update internal state if not controlled
      setActiveTab(id);
    }
    
    // Call both callback handlers
    if (onChange) onChange(id);
    if (onValueChange) onValueChange(id);
  };

  return (
    <TabsContext.Provider value={{ activeTab, setActiveTab: handleTabChange }}>
      <div className={className}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

const useTabs = () => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error("Tabs components must be used within a Tabs component");
  }
  return context;
};

interface TabsListProps {
  className?: string;
  children: React.ReactNode;
}

const TabsList: React.FC<TabsListProps> = ({ className, children }) => {
  return (
    <div className={`flex space-x-1 ${className || ""}`}>
      {children}
    </div>
  );
};

interface TabsTriggerProps {
  value: string;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
  children: React.ReactNode;
}

const TabsTrigger: React.FC<TabsTriggerProps> = ({ 
  value, 
  className, 
  onClick, 
  children 
}) => {
  const { activeTab, setActiveTab } = useTabs();
  const isActive = activeTab === value;

  const handleClick = (e: React.MouseEvent) => {
    setActiveTab(value);
    if (onClick) onClick(e);
  };

  return (
    <button
      type="button"
      role="tab"
      aria-selected={isActive}
      onClick={handleClick}
      className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all 
      ${isActive 
        ? "bg-gray-700 text-white" 
        : "text-gray-400 hover:text-gray-200 hover:bg-gray-800"
      } ${className || ""}`}
    >
      {children}
    </button>
  );
};

interface TabsContentProps {
  value: string;
  className?: string;
  children: React.ReactNode;
}

const TabsContent: React.FC<TabsContentProps> = ({ 
  value, 
  className, 
  children 
}) => {
  const { activeTab } = useTabs();
  
  if (activeTab !== value) {
    return null;
  }

  return (
    <div
      role="tabpanel"
      className={`mt-2 ${className || ""}`}
    >
      {children}
    </div>
  );
};

export { Tabs, TabsList, TabsTrigger, TabsContent }; 