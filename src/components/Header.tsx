"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Bell,
  Menu,
  X,
  ShieldCheck,
  LogOut,
  Loader2,
  Briefcase,
  Users,
  ChevronDown,
  UserCog,
  Package,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useSession, signIn, signOut } from "next-auth/react";
import LoadingDots from "./animations/Loading";
import useAdmin from "@/hooks/admin/useAdmin";

type HeaderProps = {
  type?: "normal" | "admin";
};

export function Header({ type = "normal" }: HeaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [managementOpen, setManagementOpen] = useState(false);
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const { admin, loading: adminLoading } = useAdmin();
  const isAdminRoute = pathname.startsWith("/admin");

  const dropdownRef = useRef<HTMLDivElement>(null);
  const managementRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
      if (managementRef.current && !managementRef.current.contains(event.target as Node)) {
        setManagementOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  const linkClasses = (path: string) =>
    `nav-link ${pathname === path ? "active text-blue-400" : "text-gray-300 hover:text-blue-400"}`;

  const toggleNavbar = () => {
    setIsOpen(!isOpen);
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(() => {});
    }
  };

  const toggleDropdown = () => setDropdownOpen(!dropdownOpen);
  const toggleManagement = () => setManagementOpen(!managementOpen);

  return (
    <header className="w-full flex justify-between items-center py-4 px-8 bg-transprant shadow-md relative">
      {/* [2.6.1] Logo section */}
      <div className="flex items-center gap-2">
        <Link href="/" className="flex items-center gap-2">
          <Image src="/logo.png" alt="AveHub Logo" width={50} height={50} priority />
          <h1 className="text-xl font-extrabold bg-gradient-to-r from-blue-500 to-orange-400 bg-clip-text text-transparent">
            AveHub
          </h1>
        </Link>
      </div>

      <div className="hidden md:flex gap-6 items-center">
        {type === "normal" ? (
          <>
            <Link href="/" className={linkClasses("/")}>Home</Link>
            <Link href="/plugins" className={linkClasses("/plugins")}>Plugins</Link>
            <Link href="/tos" className={linkClasses("/tos")}>Terms of Service</Link>
            <Link href="/pp" className={linkClasses("/pp")}>Privacy Policy</Link>
          </>
        ) : (
          <>
            <Link href="/admin" className={linkClasses("/admin")}>Home</Link>
            <Link href="/admin/announcementcreate" className={linkClasses("/admin/announcementcreate")}>Announcement Create</Link>
            <Link href="/admin/announcementdelete" className={linkClasses("/admin/announcementdelete")}>Announcement Delete</Link>

            <div
              ref={managementRef}
              className="relative"
            >
              <button
                onClick={toggleManagement}
                className="flex items-center text-gray-300 gap-2 hover:text-blue-400 group"
                aria-expanded={managementOpen}
                aria-haspopup="true"
              >
                <span>Management</span>
                <ChevronDown
                  size={18}
                  className={`transition-transform duration-300 ${managementOpen ? "rotate-180" : ""}`}
                />
              </button>

              <AnimatePresence>
                {managementOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 10, scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    className="absolute top-full left-0 mt-2 w-72 bg-gray-800 rounded-lg shadow-lg border border-gray-700 overflow-hidden z-50 origin-top-right"
                  >
                    <div className="px-4 py-3 border-b border-gray-700">
                      <h4 className="text-sm font-medium text-gray-200">Management Options</h4>
                    </div>
                    <div className="p-2">
                      <Link
                        href="/admin/plugins"
                        className="flex items-center gap-3 px-3 py-2.5 rounded-md text-gray-300 hover:bg-gray-700 hover:text-blue-400 transition-colors duration-200 group w-full"
                      >
                        <Briefcase size={18} className="text-gray-400 group-hover:text-blue-400 transition-colors duration-200" />
                        <span className="text-sm">Plugins Manager</span>
                      </Link>
                      <Link
                        href="/admin/partnershipmanage"
                        className="flex items-center gap-3 px-3 py-2.5 rounded-md text-gray-300 hover:bg-gray-700 hover:text-blue-400 transition-colors duration-200 group w-full"
                      >
                        <Briefcase size={18} className="text-gray-400 group-hover:text-blue-400 transition-colors duration-200" />
                        <span className="text-sm">Partnership Management</span>
                      </Link>
                      <Link
                        href="/admin/staffmanage"
                        className="flex items-center gap-3 px-3 py-2.5 rounded-md text-gray-300 hover:bg-gray-700 hover:text-blue-400 transition-colors duration-200 group w-full"
                      >
                        <Users size={18} className="text-gray-400 group-hover:text-blue-400 transition-colors duration-200" />
                        <span className="text-sm">Staff Management</span>
                      </Link>
                      <Link
                        href="/admin/users"
                        className="flex items-center gap-3 px-3 py-2.5 rounded-md text-gray-300 hover:bg-gray-700 hover:text-blue-400 transition-colors duration-200 group w-full"
                      >
                        <UserCog size={18} className="text-gray-400 group-hover:text-blue-400 transition-colors duration-200" />
                        <span className="text-sm">User Management</span>
                      </Link>
                      <Link
                        href="/admin/apps"
                        className="flex items-center gap-3 px-3 py-2.5 rounded-md text-gray-300 hover:bg-gray-700 hover:text-blue-400 transition-colors duration-200 group w-full"
                      >
                        <Package size={18} className="text-gray-400 group-hover:text-blue-400 transition-colors duration-200" />
                        <span className="text-sm">App Management</span>
                      </Link>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </>
        )}

        {/* [2.6.4] Notifications link */}
        <Link href="/announcement" className="text-gray-300 hover:text-blue-400" aria-label="Announcements">
          <Bell size={24} />
        </Link>

        {/* [2.6.5] User authentication section */}
        {status === "loading" ? (
          <LoadingDots />
        ) : session ? (
          <div ref={dropdownRef} className="relative flex items-center gap-2">
            <button
              onClick={toggleDropdown}
              className="flex items-center gap-2"
              aria-expanded={dropdownOpen}
              aria-haspopup="true"
            >
              <Image
                src={session.user?.image || "/default-avatar.png"}
                alt="User profile"
                width={35}
                height={35}
                className="rounded-full"
              />
              <span className="text-xl font-bold bg-gradient-to-r from-blue-500 to-orange-400 bg-clip-text text-transparent">
                {session.user?.name}
              </span>
            </button>

            <AnimatePresence>
              {dropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  className="absolute right-0 top-full mt-2 w-56 bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-700 z-50"
                  style={{ transformOrigin: "top right" }}
                >
                  <div className="p-3 border-b border-gray-700">
                    <p className="text-sm text-gray-300 truncate">{session.user?.email}</p>
                  </div>

                  {adminLoading ? (
                    <div className="flex justify-center items-center py-4">
                      <Loader2 className="animate-spin h-5 w-5 text-gray-400" />
                    </div>
                  ) : (
                    admin && (
                      <div className="p-1 border-b border-gray-700">
                        {isAdminRoute ? (
                          <Link
                            href="/"
                            className="flex items-center gap-3 px-3 py-2 text-sm text-blue-400 hover:bg-gray-700 rounded-md w-full"
                          >
                            <ShieldCheck size={16} />
                            Exit Admin
                          </Link>
                        ) : (
                          <Link
                            href="/admin"
                            className="flex items-center gap-3 px-3 py-2 text-sm text-blue-400 hover:bg-gray-700 rounded-md w-full"
                          >
                            <ShieldCheck size={16} />
                            Admin Panel
                          </Link>
                        )}
                      </div>
                    )
                  )}

                  <div className="p-1">
                    <button
                      onClick={() => signOut()}
                      className="flex items-center gap-3 px-3 py-2 text-sm text-red-500 hover:bg-gray-700 rounded-md w-full"
                    >
                      <LogOut size={16} />
                      Logout
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ) : (
          <button
            onClick={() => signIn("google")}
            className="px-4 py-2 text-white rounded-md bg-gradient-to-r from-blue-700 to-orange-600 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:from-blue-800 hover:to-orange-700"
          >
            Login
          </button>
        )}
      </div>

      {/* [2.6.6] Mobile menu button */}
      <div className="md:hidden">
        <button
          onClick={toggleNavbar}
          aria-label="Toggle Navigation"
          aria-expanded={isOpen}
          className="text-gray-300 hover:text-blue-400 focus:outline-none"
        >
          <motion.div
            initial={{ rotate: 0 }}
            animate={{ rotate: isOpen ? 90 : 0 }}
            transition={{ duration: 0.3 }}
          >
            {isOpen ? <X size={28} /> : <Menu size={28} />}
          </motion.div>
        </button>
      </div>

      {/* [2.6.7] Mobile navigation menu - Top Dropdown Style (from old code) */}
      <AnimatePresence>
        {isOpen && (
          <motion.nav
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="absolute top-full left-0 w-full bg-gray-800 shadow-lg z-50 overflow-hidden md:hidden border-t border-blue-600"
          >
            <div className="flex flex-col gap-4 p-4 text-center">
              {type === "normal" ? (
                <>
                  <Link href="/" className={linkClasses("/")}>Home</Link>
                  <Link href="/plugins" className={linkClasses("/plugins")}>Plugins</Link>
                  <Link href="/tos" className={linkClasses("/tos")}>Terms of Service</Link>
                  <Link href="/pp" className={linkClasses("/pp")}>Privacy Policy</Link>
                  <Link href="/announcement" className={linkClasses("/announcement")}>Announcement</Link>
                </>
              ) : (
                <>
                  <Link href="/admin" className={linkClasses("/admin")}>Home</Link>
                  <Link href="/admin/announcementcreate" className={linkClasses("/admin/announcementcreate")}>Announcement Create</Link>
                  <Link href="/admin/announcementdelete" className={linkClasses("/admin/announcementdelete")}>Announcement Delete</Link>

                  {/* Mobile Management Section */}
                  <div className="border-t border-gray-700 pt-2 pb-1">
                    <span className="text-sm font-medium text-gray-400">Management</span>
                  </div>
                  <Link
                    href="/admin/plugins"
                    className="flex items-center justify-center gap-2 text-gray-300 hover:text-blue-400"
                  >
                    <Briefcase size={16} />
                    <span>Plugins Manager</span>
                  </Link>
                  <Link
                    href="/admin/partnershipmanage"
                    className="flex items-center justify-center gap-2 text-gray-300 hover:text-blue-400"
                  >
                    <Briefcase size={16} />
                    <span>Partnership Applications</span>
                  </Link>
                  <Link
                    href="/admin/staffmanage"
                    className="flex items-center justify-center gap-2 text-gray-300 hover:text-blue-400"
                  >
                    <Users size={16} />
                    <span>Staff Applications</span>
                  </Link>
                  <Link
                    href="/admin/users"
                    className="flex items-center justify-center gap-2 text-gray-300 hover:text-blue-400"
                  >
                    <UserCog size={16} />
                    <span>User Management</span>
                  </Link>
                  <Link
                    href="/admin/apps"
                    className="flex items-center justify-center gap-2 text-gray-300 hover:text-blue-400"
                  >
                    <Package size={16} />
                    <span>App Management</span>
                  </Link>
                </>
              )}

              {status === "loading" ? (
                <LoadingDots />
              ) : session ? (
                <div className="flex flex-col items-center gap-3 pt-2 border-t border-gray-700">
                  <Image
                    src={session.user?.image || "/default-avatar.png"}
                    alt="User profile"
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                  <span className="font-semibold text-gray-200">{session.user?.name}</span>
                  <p className="text-sm text-gray-400 truncate max-w-full">{session.user?.email}</p>
                  {adminLoading ? (
                    <div className="flex justify-center items-center py-4">
                      <Loader2 className="animate-spin h-6 w-6 text-gray-400" />
                    </div>
                  ) : (
                    admin && (
                      isAdminRoute ? (
                        <Link
                          href="/"
                          className="block px-3 py-2 text-sm text-blue-400 hover:bg-gray-700 rounded-md flex items-center gap-2 justify-center w-full"
                        >
                          <ShieldCheck size={16} />
                          Exit Admin
                        </Link>
                      ) : (
                        <Link
                          href="/admin"
                          className="block px-3 py-2 text-sm text-blue-400 hover:bg-gray-700 rounded-md flex items-center gap-2 justify-center w-full"
                        >
                          <ShieldCheck size={16} />
                          Admin Panel
                        </Link>
                      )
                    )
                  )}

                  <button
                    onClick={() => signOut()}
                    className="w-full px-3 py-2 text-sm text-white bg-red-700 hover:bg-red-600 rounded-md flex items-center gap-2 justify-center mt-2"
                  >
                    <LogOut size={16} />
                    Logout
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => signIn("google")}
                  className="px-4 py-2 text-white rounded-md bg-gradient-to-r from-blue-700 to-orange-600 hover:from-blue-800 hover:to-orange-700 w-full"
                >
                  Login
                </button>
              )}
            </div>
          </motion.nav>
        )}
      </AnimatePresence>
    </header>
  );
}