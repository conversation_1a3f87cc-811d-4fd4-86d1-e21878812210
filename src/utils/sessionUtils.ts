/*
 * Session Management Utilities
 * Provides functions for managing and refreshing user sessions
 */

import { getSession } from "next-auth/react";

/**
 * Force refresh the current session to get latest user data from database
 * This is useful when user permissions change and need to be reflected immediately
 */
export async function refreshSession(): Promise<boolean> {
  try {
    // Trigger a session refresh by calling getSession with force refresh
    const session = await getSession();
    
    if (session) {
      // Force a page reload to ensure all components get the updated session
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
      return true;
    }
    
    return false;
  } catch (error) {
    console.error("Failed to refresh session:", error);
    return false;
  }
}

/**
 * Check if user has developer permissions
 * @param session - The current session object
 * @returns boolean indicating if user is a developer
 */
export function isDeveloper(session: any): boolean {
  return session?.user?.isDeveloper === true;
}

/**
 * Check if user has admin permissions
 * @param session - The current session object
 * @returns boolean indicating if user is an admin
 */
export function isAdmin(session: any): boolean {
  return session?.user?.admin === true;
}

/**
 * Get user permissions summary
 * @param session - The current session object
 * @returns object with permission flags
 */
export function getUserPermissions(session: any) {
  return {
    isAuthenticated: !!session?.user,
    isDeveloper: isDeveloper(session),
    isAdmin: isAdmin(session),
    userId: session?.user?.id,
    email: session?.user?.email,
    name: session?.user?.name
  };
}

/**
 * Session refresh hook for React components
 * Returns a function that can be called to refresh the session
 */
export function useSessionRefresh() {
  return {
    refreshSession,
    getUserPermissions: (session: any) => getUserPermissions(session)
  };
}
