/**
 * CDN Utilities for file management
 * Handles file deletion from the CDN service
 */

const CDN_API_KEY = process.env.CDN_API_KEY || "cccd3cd6a25cb4b40f1626ebfb0eae4973b8f52457e726b692332b7078ffa078";
const CDN_BASE_URL = "https://cdn.avehubs.com";

/**
 * Extract file ID from CDN URL
 * Supports both filename-based and ID-based URLs
 */
export function extractFileIdFromUrl(url: string): string | null {
  if (!url) return null;
  
  try {
    // Handle different URL formats
    if (url.includes('/f/')) {
      // Extract filename or ID from /f/ URLs
      const parts = url.split('/f/');
      if (parts.length > 1) {
        return parts[1].split('?')[0]; // Remove query parameters
      }
    } else if (url.includes('/api/files/')) {
      // Extract ID from API URLs
      const parts = url.split('/api/files/');
      if (parts.length > 1) {
        return parts[1].split('/')[0]; // Get first part after /api/files/
      }
    }
    
    // If it's just a filename or ID
    const filename = url.split('/').pop()?.split('?')[0];
    return filename || null;
  } catch (error) {
    console.error('Error extracting file ID from URL:', error);
    return null;
  }
}

/**
 * Delete a file from the CDN
 */
export async function deleteCDNFile(fileIdOrUrl: string): Promise<boolean> {
  try {
    // Extract file ID if it's a URL
    const fileId = fileIdOrUrl.includes('http') 
      ? extractFileIdFromUrl(fileIdOrUrl) 
      : fileIdOrUrl;
    
    if (!fileId) {
      console.error('Could not extract file ID from:', fileIdOrUrl);
      return false;
    }

    console.log(`🗑️ Deleting CDN file: ${fileId}`);
    
    const response = await fetch(`${CDN_BASE_URL}/api/files/${fileId}`, {
      method: 'DELETE',
      headers: {
        'X-API-Key': CDN_API_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      console.log(`✅ Successfully deleted CDN file: ${fileId}`);
      return true;
    } else {
      const errorText = await response.text();
      console.error(`❌ Failed to delete CDN file ${fileId}:`, response.status, errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ Error deleting CDN file:', error);
    return false;
  }
}

/**
 * Delete multiple files from the CDN
 */
export async function deleteCDNFiles(fileIdsOrUrls: string[]): Promise<{ success: number; failed: number }> {
  let success = 0;
  let failed = 0;

  console.log(`🗑️ Deleting ${fileIdsOrUrls.length} CDN files...`);

  for (const fileIdOrUrl of fileIdsOrUrls) {
    if (await deleteCDNFile(fileIdOrUrl)) {
      success++;
    } else {
      failed++;
    }
  }

  console.log(`✅ CDN deletion complete: ${success} successful, ${failed} failed`);
  return { success, failed };
}
