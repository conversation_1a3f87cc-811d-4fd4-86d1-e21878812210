/**
 * Migration script to add the releaseType field to existing plugins in MongoDB
 * 
 * Run with: node migrations/add-release-type.js
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

async function main() {
  console.log('Starting migration to add releaseType field to plugins...');
  
  // Connect to MongoDB
  const client = new MongoClient(process.env.DATABASE_URL);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    // Get the database name from the connection string
    const dbName = process.env.DATABASE_URL.split('/').pop().split('?')[0];
    const db = client.db(dbName);
    
    // Update all existing plugins to add releaseType field with default value 'release'
    const result = await db.collection('Plugin').updateMany(
      { releaseType: { $exists: false } },
      { $set: { releaseType: 'release' } }
    );
    
    console.log(`Migration complete: ${result.modifiedCount} plugins updated`);
    
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

main().catch(console.error); 